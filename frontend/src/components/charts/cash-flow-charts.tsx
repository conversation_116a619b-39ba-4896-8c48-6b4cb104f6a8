'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  LineChart,
  Line,
  AreaChart,
  Area,
  ComposedChart,
  ReferenceLine,
  Cell,
  PieChart,
  Pie
} from 'recharts';

interface CashFlowChartsProps {
  data: {
    statements: any[];
    forecast: any;
    seasonal: any;
    analytics: any;
  };
}

export default function CashFlowCharts({ data }: CashFlowChartsProps) {
  // Prepare cash flow statement data
  const cashFlowData = data.statements?.map((statement, index) => ({
    month: `Month ${index + 1}`,
    operating: statement.net_operating_cash_flow,
    investing: statement.net_investing_cash_flow,
    financing: statement.net_financing_cash_flow,
    netCashFlow: statement.net_change_in_cash,
    inflows: statement.operating_cash_inflows + statement.investing_cash_inflows + statement.financing_cash_inflows,
    outflows: statement.operating_cash_outflows + statement.investing_cash_outflows + statement.financing_cash_outflows
  })) || [];

  // Prepare forecast data
  const forecastData = data.forecast?.monthly_forecasts?.map((forecast, index) => ({
    month: forecast.month,
    actual: index < 6 ? cashFlowData[index]?.netCashFlow || 0 : null,
    forecast: forecast.net_operating_flow,
    confidence: forecast.confidence
  })) || [];

  // Prepare seasonal data
  const seasonalData = data.seasonal?.monthly_averages ? 
    Object.entries(data.seasonal.monthly_averages).map(([month, value]) => ({
      month: new Date(2023, parseInt(month) - 1).toLocaleString('default', { month: 'short' }),
      cashFlow: value as number,
      pattern: data.seasonal.monthly_patterns?.[month] || 'MEDIUM'
    })) : [];

  // Cash flow breakdown pie chart data
  const breakdownData = cashFlowData.length > 0 ? [
    { 
      name: 'Operating', 
      value: Math.abs(cashFlowData.reduce((sum, item) => sum + item.operating, 0)),
      color: '#10B981'
    },
    { 
      name: 'Investing', 
      value: Math.abs(cashFlowData.reduce((sum, item) => sum + item.investing, 0)),
      color: '#3B82F6'
    },
    { 
      name: 'Financing', 
      value: Math.abs(cashFlowData.reduce((sum, item) => sum + item.financing, 0)),
      color: '#8B5CF6'
    }
  ] : [];

  const COLORS = ['#10B981', '#3B82F6', '#8B5CF6'];

  return (
    <div className="space-y-6">
      {/* Cash Flow Overview */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Cash Flow Trends</CardTitle>
            <CardDescription>Monthly cash flow performance</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={cashFlowData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip formatter={(value) => [`₹${(value as number / 100000).toFixed(1)}L`, '']} />
                <Area 
                  type="monotone" 
                  dataKey="netCashFlow" 
                  stroke="#3B82F6" 
                  fill="#3B82F6" 
                  fillOpacity={0.3}
                  name="Net Cash Flow"
                />
                <ReferenceLine y={0} stroke="#EF4444" strokeDasharray="2 2" />
              </AreaChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Cash Flow Breakdown</CardTitle>
            <CardDescription>Distribution by activity type</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={breakdownData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, value }) => `${name}: ₹${(value / 100000).toFixed(1)}L`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {breakdownData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip formatter={(value) => [`₹${(value as number / 100000).toFixed(1)}L`, '']} />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Cash Flow Analysis */}
      <Card>
        <CardHeader>
          <CardTitle>Cash Flow by Category</CardTitle>
          <CardDescription>Operating, investing, and financing activities</CardDescription>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={400}>
            <ComposedChart data={cashFlowData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip formatter={(value) => [`₹${(value as number / 100000).toFixed(1)}L`, '']} />
              <Bar dataKey="operating" fill="#10B981" name="Operating" />
              <Bar dataKey="investing" fill="#3B82F6" name="Investing" />
              <Bar dataKey="financing" fill="#8B5CF6" name="Financing" />
              <Line type="monotone" dataKey="netCashFlow" stroke="#EF4444" strokeWidth={2} name="Net Cash Flow" />
              <ReferenceLine y={0} stroke="#6B7280" strokeDasharray="2 2" />
            </ComposedChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Inflows vs Outflows */}
      <Card>
        <CardHeader>
          <CardTitle>Cash Inflows vs Outflows</CardTitle>
          <CardDescription>Monthly cash generation and usage</CardDescription>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={cashFlowData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip formatter={(value) => [`₹${(value as number / 100000).toFixed(1)}L`, '']} />
              <Bar dataKey="inflows" fill="#10B981" name="Inflows" />
              <Bar dataKey="outflows" fill="#EF4444" name="Outflows" />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Forecast Analysis */}
      {forecastData.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Cash Flow Forecast</CardTitle>
            <CardDescription>Actual vs projected cash flows</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={forecastData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip formatter={(value) => [`₹${(value as number / 100000).toFixed(1)}L`, '']} />
                <Line 
                  type="monotone" 
                  dataKey="actual" 
                  stroke="#3B82F6" 
                  strokeWidth={2} 
                  name="Actual"
                  connectNulls={false}
                />
                <Line 
                  type="monotone" 
                  dataKey="forecast" 
                  stroke="#F59E0B" 
                  strokeWidth={2} 
                  strokeDasharray="5 5"
                  name="Forecast"
                />
                <ReferenceLine y={0} stroke="#EF4444" strokeDasharray="2 2" />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      )}

      {/* Seasonal Patterns */}
      {seasonalData.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Seasonal Cash Flow Patterns</CardTitle>
            <CardDescription>Monthly seasonal variations</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={seasonalData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip 
                  formatter={(value) => [`₹${(value as number / 100000).toFixed(1)}L`, '']}
                  labelFormatter={(label) => `Month: ${label}`}
                />
                <Bar 
                  dataKey="cashFlow" 
                  name="Average Cash Flow"
                  fill={(entry) => {
                    const pattern = seasonalData.find(d => d.month === entry)?.pattern;
                    return pattern === 'HIGH' ? '#10B981' : pattern === 'LOW' ? '#EF4444' : '#F59E0B';
                  }}
                >
                  {seasonalData.map((entry, index) => (
                    <Cell 
                      key={`cell-${index}`} 
                      fill={entry.pattern === 'HIGH' ? '#10B981' : entry.pattern === 'LOW' ? '#EF4444' : '#F59E0B'} 
                    />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      )}

      {/* Cash Flow Health Metrics */}
      <div className="grid gap-6 md:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle>Cash Flow Quality</CardTitle>
            <CardDescription>Overall cash flow health</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600 mb-2">
                  {data.analytics?.cash_flow_health_score?.toFixed(0) || 0}/100
                </div>
                <Badge variant={
                  (data.analytics?.cash_flow_health_score || 0) >= 80 ? "default" : 
                  (data.analytics?.cash_flow_health_score || 0) >= 60 ? "secondary" : "destructive"
                }>
                  {(data.analytics?.cash_flow_health_score || 0) >= 80 ? "Excellent" : 
                   (data.analytics?.cash_flow_health_score || 0) >= 60 ? "Good" : "Needs Improvement"}
                </Badge>
              </div>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Predictability</span>
                  <span className="font-medium">{data.analytics?.cash_flow_predictability?.toFixed(0) || 0}%</span>
                </div>
                <div className="flex justify-between">
                  <span>Volatility</span>
                  <span className="font-medium">{data.analytics?.cash_flow_volatility?.toFixed(0) || 0}%</span>
                </div>
                <div className="flex justify-between">
                  <span>Growth Rate</span>
                  <span className="font-medium">{data.analytics?.cash_flow_growth_rate?.toFixed(1) || 0}%</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Liquidity Position</CardTitle>
            <CardDescription>Current liquidity metrics</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600 mb-2">
                  {data.analytics?.liquidity_score?.toFixed(0) || 0}/100
                </div>
                <Badge variant={
                  (data.analytics?.liquidity_score || 0) >= 80 ? "default" : 
                  (data.analytics?.liquidity_score || 0) >= 60 ? "secondary" : "destructive"
                }>
                  {(data.analytics?.liquidity_score || 0) >= 80 ? "Strong" : 
                   (data.analytics?.liquidity_score || 0) >= 60 ? "Adequate" : "Weak"}
                </Badge>
              </div>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Free Cash Flow</span>
                  <span className="font-medium">₹{((data.analytics?.free_cash_flow || 0) / 100000).toFixed(1)}L</span>
                </div>
                <div className="flex justify-between">
                  <span>Operating CF Ratio</span>
                  <span className="font-medium">{data.analytics?.operating_cash_flow_ratio?.toFixed(2) || 0}</span>
                </div>
                <div className="flex justify-between">
                  <span>Cash Ratio</span>
                  <span className="font-medium">{data.analytics?.cash_ratio?.toFixed(2) || 0}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Efficiency Metrics</CardTitle>
            <CardDescription>Cash flow efficiency indicators</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="text-center">
                <div className="text-3xl font-bold text-purple-600 mb-2">
                  {data.analytics?.efficiency_score?.toFixed(0) || 0}/100
                </div>
                <Badge variant={
                  (data.analytics?.efficiency_score || 0) >= 80 ? "default" : 
                  (data.analytics?.efficiency_score || 0) >= 60 ? "secondary" : "destructive"
                }>
                  {(data.analytics?.efficiency_score || 0) >= 80 ? "Efficient" : 
                   (data.analytics?.efficiency_score || 0) >= 60 ? "Moderate" : "Inefficient"}
                </Badge>
              </div>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Cash Turnover</span>
                  <span className="font-medium">{data.analytics?.cash_turnover_ratio?.toFixed(1) || 0}x</span>
                </div>
                <div className="flex justify-between">
                  <span>Receivables Turnover</span>
                  <span className="font-medium">{data.analytics?.receivables_turnover?.toFixed(1) || 0}x</span>
                </div>
                <div className="flex justify-between">
                  <span>Inventory Turnover</span>
                  <span className="font-medium">{data.analytics?.inventory_turnover?.toFixed(1) || 0}x</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Forecast Gaps and Surpluses */}
      {data.forecast?.projected_gaps?.length > 0 || data.forecast?.surplus_periods?.length > 0 ? (
        <div className="grid gap-6 md:grid-cols-2">
          {data.forecast.projected_gaps?.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-red-700">Projected Cash Flow Gaps</CardTitle>
                <CardDescription>Periods requiring attention</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {data.forecast.projected_gaps.map((gap: any, index: number) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                      <div>
                        <p className="font-medium text-sm">{gap.month}</p>
                        <p className="text-xs text-red-600">{gap.severity} Priority</p>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold text-red-700">-₹{(gap.gap_amount / 100000).toFixed(1)}L</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {data.forecast.surplus_periods?.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-green-700">Projected Surplus Periods</CardTitle>
                <CardDescription>Investment opportunities</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {data.forecast.surplus_periods.map((surplus: any, index: number) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                      <div>
                        <p className="font-medium text-sm">{surplus.month}</p>
                        <p className="text-xs text-green-600">{surplus.opportunity} Opportunity</p>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold text-green-700">+₹{(surplus.surplus_amount / 100000).toFixed(1)}L</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      ) : null}
    </div>
  );
}
