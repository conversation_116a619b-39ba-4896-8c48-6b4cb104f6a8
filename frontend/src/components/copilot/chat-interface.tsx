'use client';

import { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { MessageList } from './message-list';
import { QuerySuggestions } from './query-suggestions';
import { api } from '@/lib/api';
import { Send, Loader2, Menu, X, AlertCircle } from 'lucide-react';
import { CopilotQuery, CopilotResponse } from '@/types';

interface Message {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  data?: any; // For structured responses
}

interface ChatInterfaceProps {
  activeQuery?: string;
  onToggleSidebars?: () => void;
  showSidebars?: boolean;
}

export function ChatInterface({ activeQuery, onToggleSidebars, showSidebars }: ChatInterfaceProps) {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      type: 'assistant',
      content: 'Hello! I\'m your AI Copilot for Credit Chakra. I can help you analyze your MSME portfolio, track compliance, identify risks, and provide actionable insights. What would you like to explore today?',
      timestamp: new Date(),
    }
  ]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  // Handle active query from quick actions - auto-submit
  useEffect(() => {
    if (activeQuery && activeQuery.trim()) {
      setInput(activeQuery);
      // Auto-submit the query after a short delay
      const timeoutId = setTimeout(() => {
        handleSendMessage(activeQuery.trim());
      }, 100);

      return () => clearTimeout(timeoutId);
    }
  }, [activeQuery]);

  // Auto-scroll to bottom when new messages are added
  useEffect(() => {
    if (scrollAreaRef.current) {
      const scrollElement = scrollAreaRef.current.querySelector('[data-radix-scroll-area-viewport]');
      if (scrollElement) {
        scrollElement.scrollTop = scrollElement.scrollHeight;
      }
    }
  }, [messages]);

  const handleSendMessage = useCallback(async (messageText?: string) => {
    const queryText = messageText || input.trim();
    if (!queryText || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: queryText,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    if (!messageText) setInput(''); // Only clear input if not auto-submitted
    setIsLoading(true);

    try {
      // Call the actual API with timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

      // Clean message history to prevent circular references
      const cleanMessages = messages.slice(-5).map(msg => ({
        id: msg.id,
        type: msg.type,
        content: msg.content,
        timestamp: msg.timestamp.toISOString()
      }));

      const response = await api.askCopilot({
        query: userMessage.content,
        context: {
          conversation_history: cleanMessages,
          user_intent: 'analysis',
          timestamp: new Date().toISOString()
        }
      });

      clearTimeout(timeoutId);

      const assistantMessage: Message = {
        id: response.query_id,
        type: 'assistant',
        content: response.response,
        timestamp: new Date(response.timestamp),
        data: response.data
      };

      setMessages(prev => [...prev, assistantMessage]);
    } catch (error) {
      console.error('Chat API Error:', error);

      let errorContent = 'I apologize, but I encountered an error processing your request. Please try again.';

      // Provide more specific error messages
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          errorContent = 'The request timed out. Please try again with a simpler query.';
        } else if (error.message.includes('fetch') || error.message.includes('network')) {
          errorContent = 'Unable to connect to the AI service. Please check your connection and try again.';
        } else if (error.message.includes('400')) {
          errorContent = 'Invalid query format. Please rephrase your question.';
        } else if (error.message.includes('500')) {
          errorContent = 'The AI service is temporarily unavailable. Please try again in a moment.';
        }
      }

      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: errorContent,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
      if (messageText) setInput(''); // Clear input after auto-submit
    }
  }, [input, isLoading, messages]);

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
    // Escape to clear input
    if (e.key === 'Escape') {
      setInput('');
    }
  };

  const handleSuggestionClick = (suggestion: string) => {
    setInput(suggestion);
  };

  const handleSendClick = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    handleSendMessage();
  }, [handleSendMessage]);

  return (
    <div className="flex flex-col h-full bg-background">
      {/* Clean Top Bar */}
      <div className="flex items-center justify-between px-6 py-4 border-b border-border/40">
        <div className="flex items-center gap-3">
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggleSidebars}
            className="text-muted-foreground hover:text-foreground"
          >
            {showSidebars ? <X className="h-4 w-4" /> : <Menu className="h-4 w-4" />}
            <span className="ml-2 text-sm">
              {showSidebars ? 'Focus Mode' : 'Show Tools'}
            </span>
          </Button>
        </div>

        {isLoading && (
          <div className="flex items-center gap-2 text-muted-foreground">
            <Loader2 className="h-4 w-4 animate-spin" />
            <span className="text-sm">AI analyzing portfolio data...</span>
          </div>
        )}
      </div>

      {/* Spacious Messages Area */}
      <div className="flex-1 overflow-hidden">
        <ScrollArea ref={scrollAreaRef} className="h-full">
          <div className="max-w-4xl mx-auto px-6 py-8">
            {messages.length <= 1 ? (
              <div className="space-y-8">
                <div className="text-center space-y-4">
                  <h2 className="text-2xl font-light text-foreground">
                    How can I help you today?
                  </h2>
                  <p className="text-muted-foreground max-w-md mx-auto">
                    Ask me about your portfolio, compliance status, risk analysis, or any insights you need.
                  </p>
                </div>
                <QuerySuggestions onSuggestionClick={handleSuggestionClick} />
              </div>
            ) : (
              <MessageList messages={messages} />
            )}
          </div>
        </ScrollArea>
      </div>

      {/* Clean Input Area */}
      <div className="border-t border-border/40 bg-background">
        <div className="max-w-4xl mx-auto px-6 py-6">
          <div className="flex gap-3">
            <Input
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Ask me anything about your portfolio... (Press Enter to send, Esc to clear)"
              className="flex-1 border-border/60 focus:border-emerald-500 transition-colors"
              disabled={isLoading}
            />
            <Button
              onClick={handleSendClick}
              disabled={!input.trim() || isLoading}
              className="bg-emerald-600 hover:bg-emerald-700 px-6"
            >
              <Send className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
