'use client';

import { Button } from '@/components/ui/button';
import {
  AlertTriangle,
  TrendingDown,
  FileCheck,
  BarChart3,
  Building2,
  Clock
} from 'lucide-react';

interface QuickActionsProps {
  onQuerySelect: (query: string) => void;
}

const quickActions = [
  {
    title: 'High Risk Alerts',
    query: 'Show me all high priority alerts that need immediate attention',
    icon: AlertTriangle,
    count: '4',
    priority: 'high',
    category: 'risk'
  },
  {
    title: 'Score Drops',
    query: 'Which MSMEs have had significant score drops in the last 30 days?',
    icon: TrendingDown,
    count: '6',
    priority: 'medium',
    category: 'risk'
  },
  {
    title: 'Compliance Tasks',
    query: 'Show me all pending compliance tasks and upcoming deadlines',
    icon: FileCheck,
    count: '12',
    priority: 'high',
    category: 'compliance'
  },
  {
    title: 'Predictive Insights',
    query: 'Generate predictive analytics for portfolio performance over next 6 months',
    icon: BarChart3,
    count: null,
    priority: 'medium',
    category: 'analytics'
  },
  {
    title: 'Behavioral Anomalies',
    query: 'Identify MSMEs with unusual behavioral patterns or payment anomalies',
    icon: Building2,
    count: '3',
    priority: 'medium',
    category: 'analytics'
  },
  {
    title: 'Intelligent Insights',
    query: 'Show me AI-powered insights and pattern recognition analysis',
    icon: Clock,
    count: null,
    priority: 'low',
    category: 'analytics'
  },
  {
    title: 'Portfolio Stress Test',
    query: 'Run stress test scenarios on current portfolio composition',
    icon: AlertTriangle,
    count: null,
    priority: 'low',
    category: 'analytics'
  },
  {
    title: 'Sector Forecasts',
    query: 'Show sector-wise risk forecasts and emerging market trends',
    icon: TrendingDown,
    count: null,
    priority: 'low',
    category: 'analytics'
  }
];

export function QuickActions({ onQuerySelect }: QuickActionsProps) {
  return (
    <div className="h-full">
      <div className="p-6 border-b border-border/40">
        <h3 className="font-medium text-foreground">Quick Actions</h3>
        <p className="text-sm text-muted-foreground mt-1">
          Common queries and analysis
        </p>
      </div>

      <div className="p-6 space-y-2">
        {quickActions.map((action, index) => {
          const getPriorityColor = (priority: string) => {
            switch (priority) {
              case 'high': return 'text-red-600 bg-red-50';
              case 'medium': return 'text-amber-600 bg-amber-50';
              default: return 'text-muted-foreground bg-muted';
            }
          };

          return (
            <Button
              key={index}
              variant="ghost"
              className="w-full justify-start h-auto p-3 hover:bg-muted/50 transition-colors"
              onClick={() => onQuerySelect(action.query)}
            >
              <div className="flex items-center gap-3 w-full">
                <action.icon className={`h-4 w-4 ${action.priority === 'high' ? 'text-red-500' : action.priority === 'medium' ? 'text-amber-500' : 'text-muted-foreground'}`} />
                <div className="flex-1 text-left">
                  <div className="text-sm font-medium text-foreground">
                    {action.title}
                  </div>
                </div>
                {action.count && (
                  <div className={`text-xs px-2 py-1 rounded font-medium ${getPriorityColor(action.priority || 'low')}`}>
                    {action.count}
                  </div>
                )}
              </div>
            </Button>
          );
        })}
      </div>
    </div>
  );
}
