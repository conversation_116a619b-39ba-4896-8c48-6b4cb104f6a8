'use client';

import { Button } from '@/components/ui/button';

interface QuerySuggestionsProps {
  onSuggestionClick: (suggestion: string) => void;
}

const suggestions = [
  "Show me high risk MSMEs that need attention",
  "Which MSMEs had significant score drops recently?",
  "What compliance deadlines are coming up?",
  "Analyze risk distribution across branches",
  "Show manufacturing sector performance trends",
  "List overdue compliance tasks"
];

export function QuerySuggestions({ onSuggestionClick }: QuerySuggestionsProps) {
  return (
    <div className="max-w-2xl mx-auto space-y-6">
      <div className="text-center">
        <h3 className="text-lg font-medium text-foreground mb-2">
          Popular questions
        </h3>
        <p className="text-sm text-muted-foreground">
          Click on any question below or type your own
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
        {suggestions.map((suggestion, index) => (
          <Button
            key={index}
            variant="outline"
            className="h-auto p-4 text-left justify-start hover:bg-muted/50 transition-colors"
            onClick={() => onSuggestionClick(suggestion)}
          >
            <div className="text-sm text-foreground">
              {suggestion}
            </div>
          </Button>
        ))}
      </div>
    </div>
  );
}
