'use client';

import { EnhancedMessage } from './enhanced-message';

interface Message {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  data?: any;
}

interface MessageListProps {
  messages: Message[];
}

export function MessageList({ messages }: MessageListProps) {
  const renderMessage = (message: Message) => {
    try {
      // Additional validation to prevent React event objects from being passed
      if (message.data && typeof message.data === 'object') {
        if (message.data._reactName || message.data.nativeEvent || message.data.currentTarget) {
          console.warn('Detected React event object in message data, filtering out:', message.id);
          message = { ...message, data: null };
        }
      }

      return (
        <EnhancedMessage
          key={message.id}
          content={message.content}
          data={message.data}
          timestamp={message.timestamp}
          type={message.type}
        />
      );
    } catch (error) {
      console.error('Error rendering message:', error, message);
      return (
        <div key={message.id} className="p-4 border border-red-200 rounded-lg bg-red-50">
          <p className="text-sm text-red-600">Error rendering message</p>
          <p className="text-xs text-red-500 mt-1">{message.content}</p>
          <p className="text-xs text-red-400 mt-1">Error: {error instanceof Error ? error.message : 'Unknown error'}</p>
        </div>
      );
    }
  };

  return (
    <div className="space-y-4">
      {messages.map(renderMessage)}
    </div>
  );
}
