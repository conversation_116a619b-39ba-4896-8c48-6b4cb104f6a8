'use client';

import { useEffect, useState, useRef } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Input } from '@/components/ui/input';
import { Skeleton } from '@/components/ui/skeleton';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb';
import { api } from '@/lib/api';
import { Analytics, MSME } from '@/types';
import { BarChart3, Users, TrendingUp, AlertTriangle, Building2, Activity, Target, Zap, ArrowUpRight, ArrowDownRight, Minus, Search, MapPin } from 'lucide-react';

export function DashboardAnalytics() {
  const [analytics, setAnalytics] = useState<Analytics | null>(null);
  const [msmes, setMsmes] = useState<MSME[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState<MSME[]>([]);
  const [showSearchResults, setShowSearchResults] = useState(false);
  const router = useRouter();
  const searchRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    async function fetchData() {
      try {
        setLoading(true);
        const [analyticsData, portfolioData] = await Promise.all([
          api.getAnalytics(),
          api.getPortfolio()
        ]);
        setAnalytics(analyticsData);
        setMsmes(portfolioData);
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch data');
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, []);

  // Search functionality
  useEffect(() => {
    if (searchTerm.trim() === '') {
      setSearchResults([]);
      setShowSearchResults(false);
      return;
    }

    const filtered = msmes.filter(msme =>
      msme.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      msme.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
      msme.business_type.toLowerCase().includes(searchTerm.toLowerCase()) ||
      msme.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
    );

    setSearchResults(filtered);
    setShowSearchResults(true);
  }, [searchTerm, msmes]);

  // Click outside handler to close search results
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setShowSearchResults(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleSearchSelect = (msmeId: string) => {
    setSearchTerm('');
    setShowSearchResults(false);
    router.push(`/msme/${msmeId}`);
  };


  const getRiskLabel = (risk: string) => {
    switch (risk) {
      case 'green': return 'Low';
      case 'yellow': return 'Medium';
      case 'red': return 'High';
      default: return 'Unknown';
    }
  };

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        {/* Breadcrumb Skeleton */}
        <div className="space-y-2">
          <Skeleton className="h-4 w-32" />
          <Skeleton className="h-8 w-64" />
        </div>

        {/* Stats Cards Skeleton */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="relative overflow-hidden">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-4 w-4 rounded" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-16 mb-2" />
                <Skeleton className="h-3 w-32" />
              </CardContent>
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full animate-shimmer" />
            </Card>
          ))}
        </div>

        {/* Charts Skeleton */}
        <div className="grid gap-6 md:grid-cols-2">
          {[...Array(2)].map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-6 w-48" />
                <Skeleton className="h-4 w-32" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-64 w-full" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-6">
        <Card className="border-destructive">
          <CardHeader>
            <CardTitle className="text-destructive">Error Loading Analytics</CardTitle>
            <CardDescription>{error}</CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={() => window.location.reload()}>Retry</Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!analytics) return null;

  const getRiskBadgeVariant = (risk: string) => {
    switch (risk) {
      case 'green': return 'default';
      case 'yellow': return 'secondary';
      case 'red': return 'destructive';
      default: return 'outline';
    }
  };

  return (
    <TooltipProvider>
      <div className="p-6 space-y-8">
        {/* Breadcrumb */}
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbPage className="text-foreground font-medium">Analytics Dashboard</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

        {/* Header */}
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div className="space-y-2">
            <h1 className="text-4xl font-bold tracking-tight bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text">
              Analytics Overview
            </h1>
            <p className="text-lg text-muted-foreground">
              Real-time insights into your MSME portfolio performance
            </p>
          </div>
          <div className="flex gap-3">
            <Link href="/portfolio">
              <Button size="lg" className="bg-primary hover:bg-primary/90 shadow-lg hover:shadow-xl transition-all duration-200">
                <Users className="mr-2 h-5 w-5" />
                View Portfolio
                <ArrowUpRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </div>
        </div>

        {/* Search Bar */}
        <div ref={searchRef} className="relative max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search MSMEs by name, location, or business type..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 shadow-sm"
          />

          {/* Search Results Dropdown */}
          {showSearchResults && searchResults.length > 0 && (
            <div className="absolute top-full left-0 right-0 mt-1 bg-background border rounded-lg shadow-lg z-50 max-h-64 overflow-y-auto">
              {searchResults.slice(0, 8).map((msme) => (
                <div
                  key={msme.msme_id}
                  className="p-3 hover:bg-muted cursor-pointer border-b last:border-b-0"
                  onClick={() => handleSearchSelect(msme.msme_id)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium text-sm">{msme.name}</h4>
                        <Badge variant={getRiskBadgeVariant(msme.risk_band)} className="text-xs">
                          {getRiskLabel(msme.risk_band)} Risk
                        </Badge>
                      </div>
                      <div className="flex items-center gap-4 text-xs text-muted-foreground">
                        <span className="flex items-center gap-1">
                          <Building2 className="h-3 w-3" />
                          {msme.business_type}
                        </span>
                        <span className="flex items-center gap-1">
                          <MapPin className="h-3 w-3" />
                          {msme.location}
                        </span>
                        <span className="flex items-center gap-1">
                          <BarChart3 className="h-3 w-3" />
                          Score: {msme.current_score}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
              {searchResults.length > 8 && (
                <div className="p-2 text-center text-sm text-muted-foreground border-t">
                  {searchResults.length - 8} more results...
                </div>
              )}
            </div>
          )}

          {/* No Results Message */}
          {showSearchResults && searchResults.length === 0 && searchTerm.trim() !== '' && (
            <div className="absolute top-full left-0 right-0 mt-1 bg-background border rounded-lg shadow-lg z-50 p-4 text-center text-sm text-muted-foreground">
              No MSMEs found matching "{searchTerm}"
            </div>
          )}
        </div>

        {/* Analytics Cards */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          <Tooltip>
            <TooltipTrigger asChild>
              <Card className="relative overflow-hidden border-0 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900 hover:shadow-lg transition-all duration-200 cursor-pointer">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-blue-700 dark:text-blue-300">Total MSMEs</CardTitle>
                  <div className="p-2 bg-blue-500 rounded-lg">
                    <Building2 className="h-4 w-4 text-white" />
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-blue-900 dark:text-blue-100">{analytics.total_msmes}</div>
                  <p className="text-sm text-blue-600 dark:text-blue-400 flex items-center gap-1">
                    <TrendingUp className="h-3 w-3" />
                    Active businesses in portfolio
                  </p>
                </CardContent>
                <div className="absolute top-0 right-0 w-20 h-20 bg-blue-500/10 rounded-full -translate-y-10 translate-x-10" />
              </Card>
            </TooltipTrigger>
            <TooltipContent>
              <p>Total number of MSMEs in your portfolio</p>
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <Card className="relative overflow-hidden border-0 bg-gradient-to-br from-emerald-50 to-emerald-100 dark:from-emerald-950 dark:to-emerald-900 hover:shadow-lg transition-all duration-200 cursor-pointer">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-emerald-700 dark:text-emerald-300">Total Signals</CardTitle>
                  <div className="p-2 bg-emerald-500 rounded-lg">
                    <Activity className="h-4 w-4 text-white" />
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-emerald-900 dark:text-emerald-100">{analytics.total_signals}</div>
                  <p className="text-sm text-emerald-600 dark:text-emerald-400 flex items-center gap-1">
                    <Target className="h-3 w-3" />
                    Avg {analytics.average_signals_per_msme} per MSME
                  </p>
                </CardContent>
                <div className="absolute top-0 right-0 w-20 h-20 bg-emerald-500/10 rounded-full -translate-y-10 translate-x-10" />
              </Card>
            </TooltipTrigger>
            <TooltipContent>
              <p>Total data signals collected across all MSMEs</p>
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <Card className="relative overflow-hidden border-0 bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950 dark:to-purple-900 hover:shadow-lg transition-all duration-200 cursor-pointer">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-purple-700 dark:text-purple-300">Risk Distribution</CardTitle>
                  <div className="p-2 bg-purple-500 rounded-lg">
                    <BarChart3 className="h-4 w-4 text-white" />
                  </div>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-green-600">Low Risk</span>
                      <span className="font-medium">{analytics.risk_distribution.green}</span>
                    </div>
                    <Progress value={(analytics.risk_distribution.green / analytics.total_msmes) * 100} className="h-2" />
                  </div>
                  <div className="flex gap-2 flex-wrap">
                    <Badge variant="outline" className="text-xs bg-green-50 text-green-700 border-green-200">
                      Low: {analytics.risk_distribution.green}
                    </Badge>
                    <Badge variant="outline" className="text-xs bg-yellow-50 text-yellow-700 border-yellow-200">
                      Med: {analytics.risk_distribution.yellow}
                    </Badge>
                    <Badge variant="outline" className="text-xs bg-red-50 text-red-700 border-red-200">
                      High: {analytics.risk_distribution.red}
                    </Badge>
                  </div>
                </CardContent>
                <div className="absolute top-0 right-0 w-20 h-20 bg-purple-500/10 rounded-full -translate-y-10 translate-x-10" />
              </Card>
            </TooltipTrigger>
            <TooltipContent>
              <p>Risk level distribution across your portfolio</p>
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <Card className="relative overflow-hidden border-0 bg-gradient-to-br from-red-50 to-red-100 dark:from-red-950 dark:to-red-900 hover:shadow-lg transition-all duration-200 cursor-pointer">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-red-700 dark:text-red-300">High Risk Alert</CardTitle>
                  <div className="p-2 bg-red-500 rounded-lg animate-pulse">
                    <AlertTriangle className="h-4 w-4 text-white" />
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-red-900 dark:text-red-100">
                    {analytics.risk_distribution.red}
                  </div>
                  <p className="text-sm text-red-600 dark:text-red-400 flex items-center gap-1">
                    <Zap className="h-3 w-3" />
                    MSMEs need immediate attention
                  </p>
                  {analytics.risk_distribution.red > 0 && (
                    <Link href="/portfolio?filter=red">
                      <Button size="sm" variant="outline" className="mt-2 text-red-700 border-red-200 hover:bg-red-50">
                        View Details
                      </Button>
                    </Link>
                  )}
                </CardContent>
                <div className="absolute top-0 right-0 w-20 h-20 bg-red-500/10 rounded-full -translate-y-10 translate-x-10" />
              </Card>
            </TooltipTrigger>
            <TooltipContent>
              <p>MSMEs requiring immediate risk assessment</p>
            </TooltipContent>
          </Tooltip>
        </div>

        {/* Business Type Distribution */}
        <div className="grid gap-6 lg:grid-cols-2">
          <Card className="border-0 shadow-lg">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-2">
                <div className="p-2 bg-primary/10 rounded-lg">
                  <Building2 className="h-5 w-5 text-primary" />
                </div>
                Business Type Distribution
              </CardTitle>
              <CardDescription>
                Portfolio composition by business category
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {Object.entries(analytics.business_type_distribution).map(([type, count], index) => {
                const percentage = (count / analytics.total_msmes) * 100;
                const colors = ['bg-blue-500', 'bg-emerald-500', 'bg-purple-500', 'bg-orange-500'];
                return (
                  <div key={type} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className={`w-3 h-3 rounded-full ${colors[index % colors.length]}`} />
                        <span className="capitalize font-medium">{type}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-muted-foreground">{percentage.toFixed(1)}%</span>
                        <Badge variant="secondary" className="font-mono">{count}</Badge>
                      </div>
                    </div>
                    <Progress value={percentage} className="h-2" />
                  </div>
                );
              })}
            </CardContent>
          </Card>

          {/* Quick Stats */}
          <Card className="border-0 shadow-lg">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-2">
                <div className="p-2 bg-emerald-500/10 rounded-lg">
                  <TrendingUp className="h-5 w-5 text-emerald-500" />
                </div>
                Portfolio Health
              </CardTitle>
              <CardDescription>
                Key performance indicators
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-4">
                <div className="flex items-center justify-between p-4 bg-green-50 dark:bg-green-950/20 rounded-lg border border-green-200 dark:border-green-800">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-green-500 rounded-lg">
                      <ArrowUpRight className="h-4 w-4 text-white" />
                    </div>
                    <div>
                      <p className="font-medium text-green-900 dark:text-green-100">Healthy MSMEs</p>
                      <p className="text-sm text-green-600 dark:text-green-400">Low risk businesses</p>
                    </div>
                  </div>
                  <div className="text-2xl font-bold text-green-900 dark:text-green-100">
                    {analytics.risk_distribution.green}
                  </div>
                </div>

                <div className="flex items-center justify-between p-4 bg-yellow-50 dark:bg-yellow-950/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-yellow-500 rounded-lg">
                      <Minus className="h-4 w-4 text-white" />
                    </div>
                    <div>
                      <p className="font-medium text-yellow-900 dark:text-yellow-100">Watch List</p>
                      <p className="text-sm text-yellow-600 dark:text-yellow-400">Medium risk businesses</p>
                    </div>
                  </div>
                  <div className="text-2xl font-bold text-yellow-900 dark:text-yellow-100">
                    {analytics.risk_distribution.yellow}
                  </div>
                </div>

                <div className="flex items-center justify-between p-4 bg-red-50 dark:bg-red-950/20 rounded-lg border border-red-200 dark:border-red-800">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-red-500 rounded-lg">
                      <ArrowDownRight className="h-4 w-4 text-white" />
                    </div>
                    <div>
                      <p className="font-medium text-red-900 dark:text-red-100">Critical</p>
                      <p className="text-sm text-red-600 dark:text-red-400">High risk businesses</p>
                    </div>
                  </div>
                  <div className="text-2xl font-bold text-red-900 dark:text-red-100">
                    {analytics.risk_distribution.red}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card className="border-0 shadow-lg bg-gradient-to-r from-primary/5 to-primary/10">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-2">
              <div className="p-2 bg-primary/10 rounded-lg">
                <Zap className="h-5 w-5 text-primary" />
              </div>
              Quick Actions
            </CardTitle>
            <CardDescription>
              Streamline your workflow with these common tasks
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-3">
              <Link href="/portfolio">
                <Button variant="outline" size="lg" className="w-full h-auto p-4 hover:bg-primary/5 hover:border-primary/20 transition-all duration-200">
                  <div className="flex flex-col items-center gap-2">
                    <Users className="h-6 w-6 text-primary" />
                    <div className="text-center">
                      <div className="font-medium">View Portfolio</div>
                      <div className="text-xs text-muted-foreground">Browse all MSMEs</div>
                    </div>
                  </div>
                </Button>
              </Link>

              <Link href="/portfolio?filter=red">
                <Button variant="outline" size="lg" className="w-full h-auto p-4 hover:bg-red-50 hover:border-red-200 transition-all duration-200">
                  <div className="flex flex-col items-center gap-2">
                    <AlertTriangle className="h-6 w-6 text-red-500" />
                    <div className="text-center">
                      <div className="font-medium">High Risk</div>
                      <div className="text-xs text-muted-foreground">Review critical cases</div>
                    </div>
                  </div>
                </Button>
              </Link>

              <Button variant="outline" size="lg" disabled className="w-full h-auto p-4 opacity-50">
                <div className="flex flex-col items-center gap-2">
                  <BarChart3 className="h-6 w-6" />
                  <div className="text-center">
                    <div className="font-medium">Generate Report</div>
                    <div className="text-xs text-muted-foreground">Coming soon</div>
                  </div>
                </div>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground bg-muted/30 rounded-lg p-4">
          <Activity className="h-4 w-4" />
          <span>Last updated: {new Date(analytics.last_updated).toLocaleString()}</span>
        </div>
      </div>
    </TooltipProvider>
  );
}
