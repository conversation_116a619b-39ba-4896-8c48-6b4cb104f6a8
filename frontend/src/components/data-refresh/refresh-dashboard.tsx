'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import RefreshStatus from './refresh-status';
import { 
  RefreshCw, 
  Play,
  Pause,
  Settings,
  Activity,
  Clock,
  CheckCircle,
  AlertTriangle,
  Database,
  TrendingUp,
  BarChart3,
  Zap,
  Calendar,
  Users
} from 'lucide-react';

interface RefreshDashboardProps {
  portfolioView?: boolean;
}

interface SystemHealth {
  system_status: string;
  last_24_hours: {
    total_jobs: number;
    completed_jobs: number;
    failed_jobs: number;
    success_rate: number;
  };
  queue_status: {
    total_queued: number;
    active_jobs: number;
    by_priority: {
      CRITICAL: number;
      HIGH: number;
      MEDIUM: number;
      LOW: number;
    };
  };
  refresh_intervals: Record<string, string>;
  performance_metrics: {
    avg_completion_time: number;
    throughput_per_hour: number;
    error_rate: number;
    system_load: number;
  };
}

export default function RefreshDashboard({ portfolioView = false }: RefreshDashboardProps) {
  const [systemHealth, setSystemHealth] = useState<SystemHealth | null>(null);
  const [loading, setLoading] = useState(true);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [refreshInterval, setRefreshInterval] = useState('30');

  useEffect(() => {
    fetchSystemHealth();
    
    if (autoRefresh) {
      const interval = setInterval(fetchSystemHealth, parseInt(refreshInterval) * 1000);
      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval]);

  const fetchSystemHealth = async () => {
    try {
      setLoading(true);
      
      // Mock system health data
      const mockData: SystemHealth = {
        system_status: "HEALTHY",
        last_24_hours: {
          total_jobs: 156,
          completed_jobs: 148,
          failed_jobs: 5,
          success_rate: 94.87
        },
        queue_status: {
          total_queued: 12,
          active_jobs: 3,
          by_priority: {
            CRITICAL: 1,
            HIGH: 3,
            MEDIUM: 6,
            LOW: 2
          }
        },
        refresh_intervals: {
          "GST": "6 hours",
          "ACCOUNT_AGGREGATOR": "2 hours",
          "UDYAM": "24 hours",
          "CASH_FLOW": "4 hours",
          "FINANCIAL_METRICS": "8 hours",
          "COMPLIANCE": "24 hours"
        },
        performance_metrics: {
          avg_completion_time: 3.2,
          throughput_per_hour: 45,
          error_rate: 3.2,
          system_load: 68.5
        }
      };
      
      setSystemHealth(mockData);
    } catch (error) {
      console.error('Error fetching system health:', error);
    } finally {
      setLoading(false);
    }
  };

  const getSystemStatusBadge = (status: string) => {
    switch (status) {
      case 'HEALTHY':
        return <Badge variant="default" className="bg-green-100 text-green-800">Healthy</Badge>;
      case 'DEGRADED':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Degraded</Badge>;
      case 'UNHEALTHY':
        return <Badge variant="destructive">Unhealthy</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'CRITICAL': return 'text-red-600';
      case 'HIGH': return 'text-orange-600';
      case 'MEDIUM': return 'text-yellow-600';
      case 'LOW': return 'text-blue-600';
      default: return 'text-gray-600';
    }
  };

  const scheduleRefresh = async (sources: string[], priority: string = 'MEDIUM') => {
    try {
      console.log('Scheduling refresh for sources:', sources, 'with priority:', priority);
      // Mock API call - in production, this would call the actual API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Refresh system health to show new jobs
      await fetchSystemHealth();
    } catch (error) {
      console.error('Error scheduling refresh:', error);
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid gap-6 md:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="h-32 bg-gray-200 rounded animate-pulse"></div>
          ))}
        </div>
        <div className="h-64 bg-gray-200 rounded animate-pulse"></div>
      </div>
    );
  }

  if (!systemHealth) {
    return (
      <div className="text-center py-12">
        <Database className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No System Data Available</h3>
        <p className="text-gray-500 mb-4">Data refresh system information is not available.</p>
        <Button onClick={fetchSystemHealth}>
          <RefreshCw className="h-4 w-4 mr-2" />
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Controls */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Data Refresh Dashboard</h2>
          <p className="text-muted-foreground">
            {portfolioView ? 'Portfolio-wide data refresh monitoring' : 'Real-time data refresh system monitoring'}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Select value={refreshInterval} onValueChange={setRefreshInterval}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="10">10 seconds</SelectItem>
              <SelectItem value="30">30 seconds</SelectItem>
              <SelectItem value="60">1 minute</SelectItem>
              <SelectItem value="300">5 minutes</SelectItem>
            </SelectContent>
          </Select>
          
          <Button
            variant="outline"
            onClick={() => setAutoRefresh(!autoRefresh)}
            className={autoRefresh ? 'bg-green-50 border-green-200' : ''}
          >
            {autoRefresh ? <Pause className="h-4 w-4 mr-2" /> : <Play className="h-4 w-4 mr-2" />}
            {autoRefresh ? 'Pause' : 'Resume'}
          </Button>
          
          <Button variant="outline" onClick={fetchSystemHealth}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* System Health Overview */}
      <div className="grid gap-6 md:grid-cols-4">
        <Card className="border-0 shadow-lg bg-gradient-to-br from-blue-50 to-blue-100">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-blue-700">
              <Activity className="h-5 w-5" />
              System Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-700 mb-2">
              {getSystemStatusBadge(systemHealth.system_status)}
            </div>
            <div className="flex items-center gap-1 text-sm">
              <span className="text-blue-600">Load: {systemHealth.performance_metrics.system_load.toFixed(0)}%</span>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-green-50 to-green-100">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-green-700">
              <CheckCircle className="h-5 w-5" />
              Success Rate
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-700 mb-2">
              {systemHealth.last_24_hours.success_rate.toFixed(1)}%
            </div>
            <div className="flex items-center gap-1 text-sm">
              <span className="text-green-600">Last 24 hours</span>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-purple-50 to-purple-100">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-purple-700">
              <Clock className="h-5 w-5" />
              Queue Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-700 mb-2">
              {systemHealth.queue_status.total_queued}
            </div>
            <div className="flex items-center gap-1 text-sm">
              <span className="text-purple-600">{systemHealth.queue_status.active_jobs} active</span>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-orange-50 to-orange-100">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-orange-700">
              <TrendingUp className="h-5 w-5" />
              Throughput
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-700 mb-2">
              {systemHealth.performance_metrics.throughput_per_hour}
            </div>
            <div className="flex items-center gap-1 text-sm">
              <span className="text-orange-600">jobs/hour</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Dashboard Tabs */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="queue">Queue Management</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Job Statistics (24h)
                </CardTitle>
                <CardDescription>Recent job execution statistics</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Total Jobs</span>
                    <span className="font-medium">{systemHealth.last_24_hours.total_jobs}</span>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Completed</span>
                    <span className="font-medium text-green-600">{systemHealth.last_24_hours.completed_jobs}</span>
                  </div>
                  <Progress 
                    value={(systemHealth.last_24_hours.completed_jobs / systemHealth.last_24_hours.total_jobs) * 100} 
                    className="h-2" 
                  />
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Failed</span>
                    <span className="font-medium text-red-600">{systemHealth.last_24_hours.failed_jobs}</span>
                  </div>
                  <Progress 
                    value={(systemHealth.last_24_hours.failed_jobs / systemHealth.last_24_hours.total_jobs) * 100} 
                    className="h-2" 
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="h-5 w-5" />
                  Performance Metrics
                </CardTitle>
                <CardDescription>System performance indicators</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between text-sm">
                    <span>Avg Completion Time</span>
                    <span className="font-medium">{systemHealth.performance_metrics.avg_completion_time.toFixed(1)}min</span>
                  </div>
                  
                  <div className="flex items-center justify-between text-sm">
                    <span>Error Rate</span>
                    <span className="font-medium">{systemHealth.performance_metrics.error_rate.toFixed(1)}%</span>
                  </div>
                  
                  <div className="flex items-center justify-between text-sm">
                    <span>System Load</span>
                    <span className="font-medium">{systemHealth.performance_metrics.system_load.toFixed(0)}%</span>
                  </div>
                  <Progress value={systemHealth.performance_metrics.system_load} className="h-2" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Data Source Refresh Intervals */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Refresh Intervals
              </CardTitle>
              <CardDescription>Configured refresh intervals for each data source</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-3">
                {Object.entries(systemHealth.refresh_intervals).map(([source, interval]) => (
                  <div key={source} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                    <div>
                      <p className="font-medium text-sm">{source.replace('_', ' ')}</p>
                      <p className="text-xs text-muted-foreground">Auto refresh</p>
                    </div>
                    <Badge variant="outline">{interval}</Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="queue" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  Queue Status
                </CardTitle>
                <CardDescription>Current job queue status</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-600 mb-2">
                    {systemHealth.queue_status.total_queued}
                  </div>
                  <p className="text-sm text-muted-foreground">Jobs in queue</p>
                </div>
                
                <Separator />
                
                <div className="space-y-3">
                  <div className="flex items-center justify-between text-sm">
                    <span>Active Jobs</span>
                    <span className="font-medium">{systemHealth.queue_status.active_jobs}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span>Queued Jobs</span>
                    <span className="font-medium">{systemHealth.queue_status.total_queued - systemHealth.queue_status.active_jobs}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5" />
                  Priority Distribution
                </CardTitle>
                <CardDescription>Jobs by priority level</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  {Object.entries(systemHealth.queue_status.by_priority).map(([priority, count]) => (
                    <div key={priority} className="flex items-center justify-between">
                      <span className={`text-sm font-medium ${getPriorityColor(priority)}`}>
                        {priority}
                      </span>
                      <Badge variant="outline" className={getPriorityColor(priority)}>
                        {count}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
              <CardDescription>Schedule refresh jobs for data sources</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-3">
                <Button 
                  onClick={() => scheduleRefresh(['GST'], 'HIGH')}
                  className="flex items-center gap-2"
                >
                  <RefreshCw className="h-4 w-4" />
                  Refresh GST Data
                </Button>
                <Button 
                  onClick={() => scheduleRefresh(['ACCOUNT_AGGREGATOR'], 'HIGH')}
                  className="flex items-center gap-2"
                >
                  <RefreshCw className="h-4 w-4" />
                  Refresh Banking Data
                </Button>
                <Button 
                  onClick={() => scheduleRefresh(['FINANCIAL_METRICS'], 'MEDIUM')}
                  className="flex items-center gap-2"
                >
                  <RefreshCw className="h-4 w-4" />
                  Refresh Financial Metrics
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>System Performance</CardTitle>
                <CardDescription>Real-time system performance metrics</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">CPU Usage</span>
                    <span className="font-medium">{systemHealth.performance_metrics.system_load.toFixed(0)}%</span>
                  </div>
                  <Progress value={systemHealth.performance_metrics.system_load} className="h-2" />
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Memory Usage</span>
                    <span className="font-medium">72%</span>
                  </div>
                  <Progress value={72} className="h-2" />
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Network I/O</span>
                    <span className="font-medium">45%</span>
                  </div>
                  <Progress value={45} className="h-2" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Job Performance</CardTitle>
                <CardDescription>Job execution performance metrics</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4 text-center">
                  <div className="p-3 bg-blue-50 rounded-lg">
                    <div className="text-lg font-bold text-blue-600">
                      {systemHealth.performance_metrics.avg_completion_time.toFixed(1)}min
                    </div>
                    <div className="text-xs text-blue-600">Avg Completion</div>
                  </div>
                  <div className="p-3 bg-green-50 rounded-lg">
                    <div className="text-lg font-bold text-green-600">
                      {systemHealth.performance_metrics.throughput_per_hour}
                    </div>
                    <div className="text-xs text-green-600">Jobs/Hour</div>
                  </div>
                  <div className="p-3 bg-red-50 rounded-lg">
                    <div className="text-lg font-bold text-red-600">
                      {systemHealth.performance_metrics.error_rate.toFixed(1)}%
                    </div>
                    <div className="text-xs text-red-600">Error Rate</div>
                  </div>
                  <div className="p-3 bg-purple-50 rounded-lg">
                    <div className="text-lg font-bold text-purple-600">
                      {systemHealth.queue_status.active_jobs}
                    </div>
                    <div className="text-xs text-purple-600">Active Jobs</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="settings" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Refresh Settings
              </CardTitle>
              <CardDescription>Configure data refresh intervals and priorities</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Auto Refresh</label>
                    <div className="flex items-center gap-2">
                      <Button
                        variant={autoRefresh ? "default" : "outline"}
                        size="sm"
                        onClick={() => setAutoRefresh(!autoRefresh)}
                      >
                        {autoRefresh ? 'Enabled' : 'Disabled'}
                      </Button>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Refresh Interval</label>
                    <Select value={refreshInterval} onValueChange={setRefreshInterval}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="10">10 seconds</SelectItem>
                        <SelectItem value="30">30 seconds</SelectItem>
                        <SelectItem value="60">1 minute</SelectItem>
                        <SelectItem value="300">5 minutes</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                
                <Separator />
                
                <div className="space-y-4">
                  <h4 className="font-medium">Data Source Settings</h4>
                  <div className="space-y-3">
                    {Object.entries(systemHealth.refresh_intervals).map(([source, interval]) => (
                      <div key={source} className="flex items-center justify-between p-3 border rounded-lg">
                        <div>
                          <p className="font-medium text-sm">{source.replace('_', ' ')}</p>
                          <p className="text-xs text-muted-foreground">Current interval: {interval}</p>
                        </div>
                        <Button variant="outline" size="sm">
                          Configure
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Real-time Status Component */}
      <RefreshStatus msmeId="" showControls={false} compact={false} />
    </div>
  );
}
