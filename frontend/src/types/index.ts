export interface MSME {
  msme_id: string;
  name: string;
  business_type: 'retail' | 'b2b' | 'manufacturing' | 'services';
  location: string;
  current_score: number;
  risk_band: 'green' | 'yellow' | 'red';
  score_trend?: 'improving' | 'declining' | 'stable';
  signals_count: number;
  recent_nudges: number;
  last_signal_date: string;
  created_at: string;
  tags: string[];
}

export interface Analytics {
  total_msmes: number;
  total_signals: number;
  risk_distribution: {
    green: number;
    yellow: number;
    red: number;
  };
  business_type_distribution: {
    [key: string]: number;
  };
  average_signals_per_msme: number;
  last_updated: string;
}

export interface ScoreDetails {
  msme_id: string;
  msme_name: string;
  current_score: number;
  risk_band: 'green' | 'yellow' | 'red';
  score_breakdown: {
    base_score: number;
    gst_penalty: number;
    reviews_penalty: number;
    upi_penalty: number;
    details: {
      [key: string]: string;
    };
  };
  signals_count: number;
  last_updated: string;
}

export type SignalSource = 'gst' | 'upi' | 'reviews' | 'justdial' | 'instagram' | 'maps';

export interface Signal {
  signal_id: string;
  msme_id: string;
  source: SignalSource;
  value: any; // Raw signal value (could be number, string, dict)
  normalized: number; // Normalized score 0-1
  timestamp: string;
  metadata: Record<string, any>;
}

export interface SignalInput {
  source: SignalSource;
  value: any;
  metadata?: Record<string, any>;
  timestamp?: string;
}

export interface Nudge {
  nudge_id: string;
  msme_id: string;
  trigger_type: 'score_drop' | 'manual' | 'risk_alert';
  message: string;
  medium: 'whatsapp' | 'email' | 'sms';
  sent_at: string;
  status: 'sent' | 'delivered' | 'failed';
  metadata: Record<string, any>;
}

export interface NudgeRequest {
  trigger_type: 'score_drop' | 'manual' | 'risk_alert';
  message: string;
  medium: 'whatsapp' | 'email' | 'sms';
  metadata?: Record<string, any>;
}

// Enhanced scoring system interfaces
export interface ScoreParameter {
  name: string;
  current_score: number;
  weight_percentage: number;
  penalty_impact: number;
  risk_level: 'low' | 'medium' | 'high';
  trend: 'improving' | 'declining' | 'stable';
  data_points: string[];
  methodology: string;
  last_updated: string;
}

export interface EnhancedScoreDetails {
  msme_id: string;
  msme_name: string;
  current_score: number;
  risk_band: 'green' | 'yellow' | 'red';
  parameters: {
    gst_compliance: ScoreParameter;
    banking_health: ScoreParameter;
    digital_payment_adoption: ScoreParameter;
    business_legitimacy: ScoreParameter;
    market_reputation: ScoreParameter;
    financial_stability: ScoreParameter;
    industry_risk_factor: ScoreParameter;
    geographic_risk: ScoreParameter;
    operational_maturity: ScoreParameter;
    compliance_history: ScoreParameter;
  };
  total_penalty_impact: number;
  signals_count: number;
  last_updated: string;
}

// Top Actions and Opportunities interfaces
export interface TopAction {
  id: string;
  title: string;
  description: string;
  impact: 'high' | 'medium' | 'low';
  type: 'risk_reduction' | 'ltv_optimization' | 'portfolio_growth' | 'efficiency';
  msme_count: number;
  potential_value: string;
  action_items: string[];
  priority_score: number;
  estimated_completion_days: number;
}

export interface PortfolioInsights {
  total_msmes: number;
  risk_distribution: {
    green: number;
    yellow: number;
    red: number;
  };
  top_actions: TopAction[];
  portfolio_health_score: number;
  diversification_index: number;
  data_completeness_score: number;
  automation_opportunities: number;
}

// Enhanced AI Copilot Types
export interface CopilotQuery {
  query: string;
  context?: {
    conversation_history?: Message[];
    user_intent?: string;
    portfolio_summary?: string;
    timestamp?: string;
  };
}

export interface CopilotResponse {
  query_id: string;
  response: string;
  confidence: number;
  reasoning?: string;
  data?: any;
  timestamp: string;
  metadata?: {
    model?: string;
    tokens?: number;
    provider?: string;
    processing_time?: number;
  };
}

export interface Message {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  data?: any;
}

export interface CopilotInsight {
  id: string;
  type: 'alert' | 'trend' | 'compliance' | 'geographic' | 'behavioral';
  severity: 'high' | 'medium' | 'low';
  title: string;
  description: string;
  timestamp: Date;
  actionable: boolean;
  dismissed?: boolean;
  priority_score?: number;
  exposure_amount?: number;
  metadata?: Record<string, any>;
}

export interface QuickAction {
  title: string;
  query: string;
  icon: any;
  count?: string;
  priority: 'high' | 'medium' | 'low';
  category?: 'risk' | 'compliance' | 'analytics' | 'operations';
}

// Advanced Risk Modeling Types
export interface RiskPrediction {
  msme_id: string;
  probability_of_default: number;
  risk_score: number;
  risk_band: 'green' | 'yellow' | 'red';
  confidence_interval: [number, number];
  feature_importance: Record<string, number>;
  model_version: string;
  prediction_timestamp: string;
  explanation: Record<string, any>;
}

export interface BehaviorProfile {
  msme_id: string;
  behavior_score: number;
  risk_indicators: string[];
  patterns: BehaviorPattern[];
  recommendations: string[];
  last_updated: string;
}

export interface BehaviorPattern {
  pattern_type: string;
  confidence: number;
  description: string;
  impact_score: number;
  trend: 'improving' | 'declining' | 'stable';
}

// Compliance and Monitoring Types
export interface ComplianceHealth {
  overall_score: number;
  overdue_tasks: number;
  upcoming_deadlines: number;
  auto_completion_rate: number;
  critical_alerts: number;
  last_updated: string;
}

export interface RiskEvent {
  id: string;
  msme_id: string;
  event_type: string;
  severity: 'high' | 'medium' | 'low';
  description: string;
  impact_score: number;
  timestamp: string;
  metadata: Record<string, any>;
}

// Portfolio Analytics Types
export interface PortfolioMetrics {
  total_exposure: number;
  portfolio_health_score: number;
  npa_ratio: number;
  average_score: number;
  risk_adjusted_return: number;
  diversification_score: number;
  stress_test_results: StressTestResult[];
}

export interface StressTestResult {
  scenario: string;
  impact_score: number;
  affected_msmes: number;
  potential_loss: number;
  mitigation_strategies: string[];
}
