#!/usr/bin/env python3
"""
Simple test script to verify the Credit Chakra API endpoints
"""
import requests
import json
from datetime import datetime

BASE_URL = "http://localhost:8000"

def test_health_check():
    """Test health check endpoint"""
    try:
        response = requests.get(f"{BASE_URL}/health")
        print(f"Health Check: {response.status_code} - {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"Health Check Failed: {e}")
        return False

def test_create_msme():
    """Test MSME creation"""
    try:
        msme_data = {
            "name": "Test MSME Business",
            "business_type": "retail",
            "location": "Mumbai, Maharashtra",
            "tags": ["retail", "electronics"]
        }

        response = requests.post(f"{BASE_URL}/msme/", json=msme_data)
        print(f"Create MSME: {response.status_code}")
        
        if response.status_code == 201:
            msme = response.json()
            print(f"Created MSME ID: {msme['msme_id']}")
            return msme['msme_id']
        else:
            print(f"Error: {response.text}")
            return None
            
    except Exception as e:
        print(f"Create MSME Failed: {e}")
        return None

def test_add_signal(msme_id):
    """Test adding a signal"""
    try:
        signal_data = {
            "source": "gst",
            "value": 1500000,  # 15L monthly turnover
            "metadata": {"month": "2024-01", "verified": True}
        }

        response = requests.post(f"{BASE_URL}/msme/{msme_id}/signals", json=signal_data)
        print(f"Add Signal: {response.status_code}")
        
        if response.status_code == 201:
            result = response.json()
            print(f"Added Signal ID: {result['signal_id']}")
            print(f"Score Update: {result['score_update']}")
            return result['signal_id']
        else:
            print(f"Error: {response.text}")
            return None
            
    except Exception as e:
        print(f"Add Signal Failed: {e}")
        return None

def test_send_nudge(msme_id):
    """Test sending a nudge"""
    try:
        nudge_data = {
            "trigger_type": "score_improvement",
            "message": "Congratulations! Your credit score has improved.",
            "medium": "email",
            "metadata": {"campaign": "score_update"}
        }

        response = requests.post(f"{BASE_URL}/msme/{msme_id}/nudge", json=nudge_data)
        print(f"Send Nudge: {response.status_code}")
        
        if response.status_code == 201:
            nudge = response.json()
            print(f"Sent Nudge ID: {nudge['nudge_id']}")
            return nudge['nudge_id']
        else:
            print(f"Error: {response.text}")
            return None
            
    except Exception as e:
        print(f"Send Nudge Failed: {e}")
        return None

def test_get_score(msme_id):
    """Test getting MSME health score"""
    try:
        response = requests.get(f"{BASE_URL}/msme/{msme_id}/score")
        print(f"Get Score: {response.status_code}")

        if response.status_code == 200:
            score_data = response.json()
            print(f"Current Score: {score_data['current_score']}")
            print(f"Risk Band: {score_data['risk_band']}")
            print(f"Score Breakdown: {score_data['score_breakdown']}")
            return score_data
        else:
            print(f"Error: {response.text}")
            return None

    except Exception as e:
        print(f"Get Score Failed: {e}")
        return None

def test_portfolio_summary():
    """Test getting portfolio summary"""
    try:
        response = requests.get(f"{BASE_URL}/dashboard/portfolio")
        print(f"Portfolio Summary: {response.status_code}")

        if response.status_code == 200:
            portfolio = response.json()
            print(f"Total MSMEs: {len(portfolio)}")
            for msme in portfolio[:3]:  # Show first 3
                print(f"  {msme['name']}: Score {msme['current_score']}, Risk {msme['risk_band']}")
            return portfolio
        else:
            print(f"Error: {response.text}")
            return None

    except Exception as e:
        print(f"Portfolio Summary Failed: {e}")
        return None

def test_dashboard_analytics():
    """Test getting dashboard analytics"""
    try:
        response = requests.get(f"{BASE_URL}/dashboard/analytics")
        print(f"Dashboard Analytics: {response.status_code}")

        if response.status_code == 200:
            analytics = response.json()
            print(f"Total MSMEs: {analytics['total_msmes']}")
            print(f"Risk Distribution: {analytics['risk_distribution']}")
            return analytics
        else:
            print(f"Error: {response.text}")
            return None

    except Exception as e:
        print(f"Dashboard Analytics Failed: {e}")
        return None

def main():
    """Run all tests"""
    print("=== Credit Chakra API Test ===\n")
    
    # Test health check
    if not test_health_check():
        print("Health check failed. Make sure the server is running.")
        return
    
    print()
    
    # Test MSME creation
    msme_id = test_create_msme()
    if not msme_id:
        print("MSME creation failed. Cannot proceed with other tests.")
        return
    
    print()
    
    # Test signal addition
    signal_id = test_add_signal(msme_id)
    print()
    
    # Test nudge sending
    nudge_id = test_send_nudge(msme_id)
    print()

    # Test score calculation
    score_data = test_get_score(msme_id)
    print()

    # Test portfolio summary
    portfolio = test_portfolio_summary()
    print()

    # Test dashboard analytics
    analytics = test_dashboard_analytics()
    print()

    print("=== Test Summary ===")
    print(f"MSME ID: {msme_id}")
    print(f"Signal ID: {signal_id}")
    print(f"Nudge ID: {nudge_id}")
    if score_data:
        print(f"Health Score: {score_data['current_score']} ({score_data['risk_band']})")
    if portfolio:
        print(f"Portfolio MSMEs: {len(portfolio)}")
    if analytics:
        print(f"Total Signals: {analytics['total_signals']}")

if __name__ == "__main__":
    main()
