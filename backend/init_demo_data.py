#!/usr/bin/env python3
"""
Initialize demo data for Credit Chakra platform
"""
from firebase.init import _MOCK_DATA_STORE
from datetime import datetime
import uuid

def init_demo_data():
    """Initialize the mock data store with sample MSMEs and signals"""
    
    # Sample MSMEs with calculated scores
    msmes = [
        {
            "msme_id": "tech-solutions-001",
            "name": "Tech Solutions Pvt Ltd",
            "business_type": "services",
            "location": "Bangalore, Karnataka",
            "created_at": "2025-01-01T10:00:00",
            "score": 659.0,
            "risk_band": "yellow",
            "tags": ["technology", "software", "startup"]
        },
        {
            "msme_id": "mumbai-textiles-002",
            "name": "Mumbai Textiles Co",
            "business_type": "manufacturing",
            "location": "Mumbai, Maharashtra",
            "created_at": "2025-01-01T10:15:00",
            "score": 801.0,
            "risk_band": "green",
            "tags": ["textiles", "export", "established"]
        },
        {
            "msme_id": "green-grocers-003",
            "name": "Green Grocers Delhi",
            "business_type": "retail",
            "location": "Delhi, NCR",
            "created_at": "2025-01-01T10:30:00",
            "score": 331.0,
            "risk_band": "red",
            "tags": ["grocery", "organic", "local"]
        },
        {
            "msme_id": "autoparts-supply-004",
            "name": "AutoParts Supply Chain",
            "business_type": "b2b",
            "location": "Chennai, Tamil Nadu",
            "created_at": "2025-01-01T10:45:00",
            "score": 630.0,
            "risk_band": "yellow",
            "tags": ["automotive", "supply-chain", "b2b"]
        },
        {
            "msme_id": "pune-food-005",
            "name": "Pune Food Processing",
            "business_type": "manufacturing",
            "location": "Pune, Maharashtra",
            "created_at": "2025-01-01T11:00:00",
            "score": 416.0,
            "risk_band": "yellow",
            "tags": ["food-processing", "fmcg", "regional"]
        },
        {
            "msme_id": "rajesh-electronics-006",
            "name": "Rajesh Electronics Store",
            "business_type": "retail",
            "location": "Jaipur, Rajasthan",
            "created_at": "2024-12-15T09:00:00",
            "score": 742.0,
            "risk_band": "green",
            "tags": ["electronics", "retail", "consumer"]
        },
        {
            "msme_id": "spice-garden-007",
            "name": "Spice Garden Restaurant",
            "business_type": "services",
            "location": "Kochi, Kerala",
            "created_at": "2024-11-20T14:30:00",
            "score": 567.0,
            "risk_band": "yellow",
            "tags": ["restaurant", "hospitality", "food"]
        },
        {
            "msme_id": "steel-works-008",
            "name": "Ahmedabad Steel Works",
            "business_type": "manufacturing",
            "location": "Ahmedabad, Gujarat",
            "created_at": "2024-10-10T08:15:00",
            "score": 823.0,
            "risk_band": "green",
            "tags": ["steel", "manufacturing", "industrial"]
        },
        {
            "msme_id": "fashion-hub-009",
            "name": "Fashion Hub Boutique",
            "business_type": "retail",
            "location": "Kolkata, West Bengal",
            "created_at": "2024-12-01T11:45:00",
            "score": 298.0,
            "risk_band": "red",
            "tags": ["fashion", "clothing", "boutique"]
        },
        {
            "msme_id": "logistics-pro-010",
            "name": "Logistics Pro Services",
            "business_type": "b2b",
            "location": "Gurgaon, Haryana",
            "created_at": "2024-09-25T16:20:00",
            "score": 689.0,
            "risk_band": "yellow",
            "tags": ["logistics", "transport", "b2b"]
        },
        {
            "msme_id": "pharma-care-011",
            "name": "Pharma Care Distributors",
            "business_type": "b2b",
            "location": "Hyderabad, Telangana",
            "created_at": "2024-08-12T13:10:00",
            "score": 756.0,
            "risk_band": "green",
            "tags": ["pharmaceutical", "healthcare", "distribution"]
        },
        {
            "msme_id": "coffee-corner-012",
            "name": "Coffee Corner Cafe",
            "business_type": "services",
            "location": "Pune, Maharashtra",
            "created_at": "2024-11-05T07:30:00",
            "score": 445.0,
            "risk_band": "yellow",
            "tags": ["cafe", "beverages", "hospitality"]
        },
        {
            "msme_id": "furniture-craft-013",
            "name": "Furniture Craft Workshop",
            "business_type": "manufacturing",
            "location": "Jodhpur, Rajasthan",
            "created_at": "2024-07-18T12:00:00",
            "score": 612.0,
            "risk_band": "yellow",
            "tags": ["furniture", "woodwork", "handicraft"]
        },
        {
            "msme_id": "mobile-repair-014",
            "name": "Quick Mobile Repair",
            "business_type": "services",
            "location": "Indore, Madhya Pradesh",
            "created_at": "2024-12-20T15:45:00",
            "score": 387.0,
            "risk_band": "red",
            "tags": ["mobile", "repair", "electronics"]
        },
        {
            "msme_id": "agri-supply-015",
            "name": "Agri Supply Chain",
            "business_type": "b2b",
            "location": "Nashik, Maharashtra",
            "created_at": "2024-06-30T10:20:00",
            "score": 701.0,
            "risk_band": "green",
            "tags": ["agriculture", "supply-chain", "farming"]
        },
        {
            "msme_id": "beauty-salon-016",
            "name": "Glamour Beauty Salon",
            "business_type": "services",
            "location": "Lucknow, Uttar Pradesh",
            "created_at": "2024-10-15T09:30:00",
            "score": 523.0,
            "risk_band": "yellow",
            "tags": ["beauty", "salon", "personal-care"]
        },
        {
            "msme_id": "bakery-fresh-017",
            "name": "Fresh Bakery & Sweets",
            "business_type": "retail",
            "location": "Bhopal, Madhya Pradesh",
            "created_at": "2024-09-08T06:15:00",
            "score": 634.0,
            "risk_band": "yellow",
            "tags": ["bakery", "sweets", "food"]
        },
        {
            "msme_id": "printing-press-018",
            "name": "Modern Printing Press",
            "business_type": "manufacturing",
            "location": "Coimbatore, Tamil Nadu",
            "created_at": "2024-05-22T14:00:00",
            "score": 778.0,
            "risk_band": "green",
            "tags": ["printing", "publishing", "media"]
        },
        {
            "msme_id": "auto-garage-019",
            "name": "City Auto Garage",
            "business_type": "services",
            "location": "Chandigarh, Punjab",
            "created_at": "2024-08-03T11:30:00",
            "score": 456.0,
            "risk_band": "yellow",
            "tags": ["automotive", "repair", "garage"]
        },
        {
            "msme_id": "handicraft-export-020",
            "name": "Heritage Handicrafts Export",
            "business_type": "b2b",
            "location": "Udaipur, Rajasthan",
            "created_at": "2024-04-10T16:45:00",
            "score": 267.0,
            "risk_band": "red",
            "tags": ["handicrafts", "export", "traditional"]
        }
    ]
    
    # Add MSMEs to store
    for msme in msmes:
        _MOCK_DATA_STORE['msmes'][msme['msme_id']] = msme
    
    # Sample signals for each MSME
    signals = [
        # Tech Solutions signals
        {
            "signal_id": str(uuid.uuid4()),
            "msme_id": "tech-solutions-001",
            "source": "gst",
            "value": 2500000,
            "normalized": 0.45,
            "timestamp": "2025-01-01T12:00:00",
            "metadata": {"period": "Q4-2024", "currency": "INR"}
        },
        {
            "signal_id": str(uuid.uuid4()),
            "msme_id": "tech-solutions-001",
            "source": "upi",
            "value": {"transaction_count": 450, "volume": 1800000},
            "normalized": 0.725,
            "timestamp": "2025-01-01T12:05:00",
            "metadata": {"period": "Q4-2024"}
        },
        {
            "signal_id": str(uuid.uuid4()),
            "msme_id": "tech-solutions-001",
            "source": "reviews",
            "value": {"average_rating": 4.3, "review_count": 87},
            "normalized": 0.804,
            "timestamp": "2025-01-01T12:10:00",
            "metadata": {"platform": "Google Reviews"}
        },
        
        # Mumbai Textiles signals
        {
            "signal_id": str(uuid.uuid4()),
            "msme_id": "mumbai-textiles-002",
            "source": "gst",
            "value": 8500000,
            "normalized": 0.805,
            "timestamp": "2025-01-01T12:15:00",
            "metadata": {"period": "Q4-2024", "currency": "INR"}
        },
        {
            "signal_id": str(uuid.uuid4()),
            "msme_id": "mumbai-textiles-002",
            "source": "reviews",
            "value": {"average_rating": 4.7, "review_count": 156},
            "normalized": 0.940,
            "timestamp": "2025-01-01T12:20:00",
            "metadata": {"platform": "Google Reviews"}
        },
        
        # Green Grocers signals
        {
            "signal_id": str(uuid.uuid4()),
            "msme_id": "green-grocers-003",
            "source": "gst",
            "value": 650000,
            "normalized": 0.195,
            "timestamp": "2025-01-01T12:25:00",
            "metadata": {"period": "Q4-2024", "currency": "INR"}
        },
        {
            "signal_id": str(uuid.uuid4()),
            "msme_id": "green-grocers-003",
            "source": "reviews",
            "value": {"average_rating": 3.8, "review_count": 23},
            "normalized": 0.467,
            "timestamp": "2025-01-01T12:30:00",
            "metadata": {"platform": "Google Reviews"}
        },
        
        # AutoParts signals
        {
            "signal_id": str(uuid.uuid4()),
            "msme_id": "autoparts-supply-004",
            "source": "gst",
            "value": 4200000,
            "normalized": 0.620,
            "timestamp": "2025-01-01T12:35:00",
            "metadata": {"period": "Q4-2024", "currency": "INR"}
        },
        {
            "signal_id": str(uuid.uuid4()),
            "msme_id": "autoparts-supply-004",
            "source": "upi",
            "value": {"transaction_count": 280, "volume": 950000},
            "normalized": 0.640,
            "timestamp": "2025-01-01T12:40:00",
            "metadata": {"period": "Q4-2024"}
        },
        
        # Pune Food signals
        {
            "signal_id": str(uuid.uuid4()),
            "msme_id": "pune-food-005",
            "source": "gst",
            "value": 3200000,
            "normalized": 0.520,
            "timestamp": "2025-01-01T12:45:00",
            "metadata": {"period": "Q4-2024", "currency": "INR"}
        },

        # Rajesh Electronics signals
        {
            "signal_id": str(uuid.uuid4()),
            "msme_id": "rajesh-electronics-006",
            "source": "gst",
            "value": 4500000,
            "normalized": 0.742,
            "timestamp": "2024-12-20T10:30:00",
            "metadata": {"period": "Q4-2024", "currency": "INR"}
        },
        {
            "signal_id": str(uuid.uuid4()),
            "msme_id": "rajesh-electronics-006",
            "source": "reviews",
            "value": {"average_rating": 4.5, "review_count": 156},
            "normalized": 0.850,
            "timestamp": "2024-12-22T14:15:00",
            "metadata": {"platform": "Google Reviews"}
        },

        # Spice Garden signals
        {
            "signal_id": str(uuid.uuid4()),
            "msme_id": "spice-garden-007",
            "source": "gst",
            "value": 1800000,
            "normalized": 0.567,
            "timestamp": "2024-12-18T09:45:00",
            "metadata": {"period": "Q4-2024", "currency": "INR"}
        },
        {
            "signal_id": str(uuid.uuid4()),
            "msme_id": "spice-garden-007",
            "source": "reviews",
            "value": {"average_rating": 4.2, "review_count": 89},
            "normalized": 0.780,
            "timestamp": "2024-12-19T16:20:00",
            "metadata": {"platform": "Zomato"}
        },

        # Steel Works signals
        {
            "signal_id": str(uuid.uuid4()),
            "msme_id": "steel-works-008",
            "source": "gst",
            "value": 8500000,
            "normalized": 0.823,
            "timestamp": "2024-12-15T11:00:00",
            "metadata": {"period": "Q4-2024", "currency": "INR"}
        },
        {
            "signal_id": str(uuid.uuid4()),
            "msme_id": "steel-works-008",
            "source": "upi",
            "value": {"transaction_count": 320, "volume": 5200000},
            "normalized": 0.890,
            "timestamp": "2024-12-16T13:30:00",
            "metadata": {"period": "Q4-2024"}
        },

        # Fashion Hub signals
        {
            "signal_id": str(uuid.uuid4()),
            "msme_id": "fashion-hub-009",
            "source": "gst",
            "value": 850000,
            "normalized": 0.298,
            "timestamp": "2024-12-10T08:15:00",
            "metadata": {"period": "Q4-2024", "currency": "INR"}
        },
        {
            "signal_id": str(uuid.uuid4()),
            "msme_id": "fashion-hub-009",
            "source": "instagram",
            "value": {"followers": 2500, "engagement_rate": 0.045},
            "normalized": 0.320,
            "timestamp": "2024-12-12T15:45:00",
            "metadata": {"verified": False}
        },

        # Logistics Pro signals
        {
            "signal_id": str(uuid.uuid4()),
            "msme_id": "logistics-pro-010",
            "source": "gst",
            "value": 3800000,
            "normalized": 0.689,
            "timestamp": "2024-12-08T12:20:00",
            "metadata": {"period": "Q4-2024", "currency": "INR"}
        },
        {
            "signal_id": str(uuid.uuid4()),
            "msme_id": "logistics-pro-010",
            "source": "upi",
            "value": {"transaction_count": 280, "volume": 2100000},
            "normalized": 0.650,
            "timestamp": "2024-12-09T14:10:00",
            "metadata": {"period": "Q4-2024"}
        },

        # Pharma Care signals
        {
            "signal_id": str(uuid.uuid4()),
            "msme_id": "pharma-care-011",
            "source": "gst",
            "value": 5200000,
            "normalized": 0.756,
            "timestamp": "2024-12-05T10:45:00",
            "metadata": {"period": "Q4-2024", "currency": "INR"}
        },
        {
            "signal_id": str(uuid.uuid4()),
            "msme_id": "pharma-care-011",
            "source": "reviews",
            "value": {"average_rating": 4.4, "review_count": 67},
            "normalized": 0.820,
            "timestamp": "2024-12-06T16:30:00",
            "metadata": {"platform": "Google Reviews"}
        },

        # Coffee Corner signals
        {
            "signal_id": str(uuid.uuid4()),
            "msme_id": "coffee-corner-012",
            "source": "gst",
            "value": 1200000,
            "normalized": 0.445,
            "timestamp": "2024-12-03T09:20:00",
            "metadata": {"period": "Q4-2024", "currency": "INR"}
        },
        {
            "signal_id": str(uuid.uuid4()),
            "msme_id": "coffee-corner-012",
            "source": "upi",
            "value": {"transaction_count": 180, "volume": 450000},
            "normalized": 0.520,
            "timestamp": "2024-12-04T11:15:00",
            "metadata": {"period": "Q4-2024"}
        },

        # Furniture Craft signals
        {
            "signal_id": str(uuid.uuid4()),
            "msme_id": "furniture-craft-013",
            "source": "gst",
            "value": 2800000,
            "normalized": 0.612,
            "timestamp": "2024-11-28T14:00:00",
            "metadata": {"period": "Q4-2024", "currency": "INR"}
        },
        {
            "signal_id": str(uuid.uuid4()),
            "msme_id": "furniture-craft-013",
            "source": "instagram",
            "value": {"followers": 3200, "engagement_rate": 0.065},
            "normalized": 0.580,
            "timestamp": "2024-11-30T12:45:00",
            "metadata": {"verified": True}
        },

        # Mobile Repair signals
        {
            "signal_id": str(uuid.uuid4()),
            "msme_id": "mobile-repair-014",
            "source": "gst",
            "value": 950000,
            "normalized": 0.387,
            "timestamp": "2024-12-25T08:30:00",
            "metadata": {"period": "Q4-2024", "currency": "INR"}
        },
        {
            "signal_id": str(uuid.uuid4()),
            "msme_id": "mobile-repair-014",
            "source": "reviews",
            "value": {"average_rating": 3.8, "review_count": 45},
            "normalized": 0.420,
            "timestamp": "2024-12-26T15:20:00",
            "metadata": {"platform": "Google Reviews"}
        },

        # Agri Supply signals
        {
            "signal_id": str(uuid.uuid4()),
            "msme_id": "agri-supply-015",
            "source": "gst",
            "value": 4200000,
            "normalized": 0.701,
            "timestamp": "2024-11-25T13:15:00",
            "metadata": {"period": "Q4-2024", "currency": "INR"}
        },
        {
            "signal_id": str(uuid.uuid4()),
            "msme_id": "agri-supply-015",
            "source": "upi",
            "value": {"transaction_count": 195, "volume": 1800000},
            "normalized": 0.720,
            "timestamp": "2024-11-26T10:40:00",
            "metadata": {"period": "Q4-2024"}
        },

        # Beauty Salon signals
        {
            "signal_id": str(uuid.uuid4()),
            "msme_id": "beauty-salon-016",
            "source": "gst",
            "value": 1600000,
            "normalized": 0.523,
            "timestamp": "2024-11-20T11:30:00",
            "metadata": {"period": "Q4-2024", "currency": "INR"}
        },
        {
            "signal_id": str(uuid.uuid4()),
            "msme_id": "beauty-salon-016",
            "source": "reviews",
            "value": {"average_rating": 4.1, "review_count": 78},
            "normalized": 0.650,
            "timestamp": "2024-11-22T14:45:00",
            "metadata": {"platform": "Google Reviews"}
        },

        # Bakery Fresh signals
        {
            "signal_id": str(uuid.uuid4()),
            "msme_id": "bakery-fresh-017",
            "source": "gst",
            "value": 2200000,
            "normalized": 0.634,
            "timestamp": "2024-11-15T07:20:00",
            "metadata": {"period": "Q4-2024", "currency": "INR"}
        },
        {
            "signal_id": str(uuid.uuid4()),
            "msme_id": "bakery-fresh-017",
            "source": "upi",
            "value": {"transaction_count": 340, "volume": 890000},
            "normalized": 0.580,
            "timestamp": "2024-11-16T09:10:00",
            "metadata": {"period": "Q4-2024"}
        },

        # Printing Press signals
        {
            "signal_id": str(uuid.uuid4()),
            "msme_id": "printing-press-018",
            "source": "gst",
            "value": 5800000,
            "normalized": 0.778,
            "timestamp": "2024-11-10T12:00:00",
            "metadata": {"period": "Q4-2024", "currency": "INR"}
        },
        {
            "signal_id": str(uuid.uuid4()),
            "msme_id": "printing-press-018",
            "source": "reviews",
            "value": {"average_rating": 4.3, "review_count": 92},
            "normalized": 0.810,
            "timestamp": "2024-11-12T16:15:00",
            "metadata": {"platform": "Google Reviews"}
        },

        # Auto Garage signals
        {
            "signal_id": str(uuid.uuid4()),
            "msme_id": "auto-garage-019",
            "source": "gst",
            "value": 1400000,
            "normalized": 0.456,
            "timestamp": "2024-11-05T10:30:00",
            "metadata": {"period": "Q4-2024", "currency": "INR"}
        },
        {
            "signal_id": str(uuid.uuid4()),
            "msme_id": "auto-garage-019",
            "source": "reviews",
            "value": {"average_rating": 3.9, "review_count": 56},
            "normalized": 0.520,
            "timestamp": "2024-11-07T13:45:00",
            "metadata": {"platform": "Google Reviews"}
        },

        # Handicraft Export signals
        {
            "signal_id": str(uuid.uuid4()),
            "msme_id": "handicraft-export-020",
            "source": "gst",
            "value": 680000,
            "normalized": 0.267,
            "timestamp": "2024-10-28T09:15:00",
            "metadata": {"period": "Q4-2024", "currency": "INR"}
        },
        {
            "signal_id": str(uuid.uuid4()),
            "msme_id": "handicraft-export-020",
            "source": "instagram",
            "value": {"followers": 1200, "engagement_rate": 0.025},
            "normalized": 0.180,
            "timestamp": "2024-10-30T14:20:00",
            "metadata": {"verified": False}
        }
    ]
    
    # Add signals to store using nested structure
    for signal in signals:
        msme_id = signal['msme_id']
        signal_id = signal['signal_id']
        
        # Create nested structure: msmes/{msme_id}/signals/{signal_id}
        nested_key = f"msmes/{msme_id}/signals"
        if nested_key not in _MOCK_DATA_STORE:
            _MOCK_DATA_STORE[nested_key] = {}
        _MOCK_DATA_STORE[nested_key][signal_id] = signal
    
    print(f"✅ Initialized demo data:")
    print(f"   📊 {len(msmes)} MSMEs")
    print(f"   📈 {len(signals)} Signals")
    print(f"   🎯 Risk distribution: {sum(1 for m in msmes if m['risk_band'] == 'green')} Green, {sum(1 for m in msmes if m['risk_band'] == 'yellow')} Yellow, {sum(1 for m in msmes if m['risk_band'] == 'red')} Red")

if __name__ == "__main__":
    init_demo_data()
