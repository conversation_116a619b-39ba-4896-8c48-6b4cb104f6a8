#!/usr/bin/env python3
"""
Comprehensive test script for Credit Chakra AI Copilot functionality
Tests all endpoints, error handling, and response quality
"""

import requests
import json
import time
from datetime import datetime

BASE_URL = "http://localhost:8000"

def test_health_check():
    """Test basic health check"""
    print("🔍 Testing health check...")
    try:
        response = requests.get(f"{BASE_URL}/health")
        print(f"✅ Health check: {response.status_code}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        return False

def test_copilot_ask():
    """Test copilot ask endpoint with various queries"""
    print("\n🤖 Testing copilot ask endpoint...")
    
    test_queries = [
        "Show me high risk MSMEs",
        "What compliance deadlines are coming up?",
        "Analyze portfolio overview",
        "Which MSMEs have declining scores?",
        "Help me understand my portfolio",
        "Invalid query with special chars @#$%"
    ]
    
    results = []
    for query in test_queries:
        try:
            payload = {
                "query": query,
                "context": {"test": True, "timestamp": datetime.now().isoformat()}
            }
            
            start_time = time.time()
            response = requests.post(
                f"{BASE_URL}/api/copilot/ask",
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Query: '{query[:30]}...' - {response.status_code} ({response_time:.2f}s)")
                print(f"   Response length: {len(data.get('response', ''))}")
                print(f"   Has structured data: {bool(data.get('data'))}")
                print(f"   Suggestions: {len(data.get('suggestions', []))}")
                results.append(True)
            else:
                print(f"❌ Query: '{query[:30]}...' - {response.status_code}")
                print(f"   Error: {response.text}")
                results.append(False)
                
        except Exception as e:
            print(f"❌ Query: '{query[:30]}...' - Exception: {e}")
            results.append(False)
    
    success_rate = sum(results) / len(results) * 100
    print(f"\n📊 Ask endpoint success rate: {success_rate:.1f}%")
    return success_rate > 80

def test_copilot_insights():
    """Test copilot insights endpoint"""
    print("\n📈 Testing copilot insights endpoint...")
    
    try:
        start_time = time.time()
        response = requests.get(f"{BASE_URL}/api/copilot/insights")
        response_time = time.time() - start_time
        
        if response.status_code == 200:
            data = response.json()
            insights = data.get('insights', [])
            
            print(f"✅ Insights endpoint: {response.status_code} ({response_time:.2f}s)")
            print(f"   Total insights: {len(insights)}")
            print(f"   High priority: {data.get('high_priority_count', 0)}")
            print(f"   Total exposure: ₹{data.get('summary', {}).get('total_exposure', 0):,.0f}")
            
            # Validate insight structure
            if insights:
                insight = insights[0]
                required_fields = ['id', 'type', 'severity', 'title', 'description', 'timestamp']
                missing_fields = [field for field in required_fields if field not in insight]
                
                if missing_fields:
                    print(f"⚠️  Missing fields in insights: {missing_fields}")
                else:
                    print("✅ Insight structure validation passed")
            
            return True
        else:
            print(f"❌ Insights endpoint: {response.status_code}")
            print(f"   Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Insights endpoint exception: {e}")
        return False

def test_error_handling():
    """Test error handling with invalid requests"""
    print("\n🛡️ Testing error handling...")
    
    test_cases = [
        {
            "name": "Empty query",
            "payload": {"query": "", "context": {}},
            "expected_status": 400
        },
        {
            "name": "Missing query field",
            "payload": {"context": {}},
            "expected_status": 422
        },
        {
            "name": "Very long query",
            "payload": {"query": "x" * 2000, "context": {}},
            "expected_status": 400
        },
        {
            "name": "Invalid JSON structure",
            "payload": "invalid json",
            "expected_status": 422
        }
    ]
    
    results = []
    for test_case in test_cases:
        try:
            if isinstance(test_case["payload"], str):
                # Test invalid JSON
                response = requests.post(
                    f"{BASE_URL}/api/copilot/ask",
                    data=test_case["payload"],
                    headers={"Content-Type": "application/json"}
                )
            else:
                response = requests.post(
                    f"{BASE_URL}/api/copilot/ask",
                    json=test_case["payload"],
                    headers={"Content-Type": "application/json"}
                )
            
            if response.status_code == test_case["expected_status"]:
                print(f"✅ {test_case['name']}: Expected {test_case['expected_status']}, got {response.status_code}")
                results.append(True)
            else:
                print(f"❌ {test_case['name']}: Expected {test_case['expected_status']}, got {response.status_code}")
                results.append(False)
                
        except Exception as e:
            print(f"❌ {test_case['name']}: Exception - {e}")
            results.append(False)
    
    success_rate = sum(results) / len(results) * 100
    print(f"\n📊 Error handling success rate: {success_rate:.1f}%")
    return success_rate > 75

def test_response_quality():
    """Test response quality and relevance"""
    print("\n📝 Testing response quality...")
    
    quality_tests = [
        {
            "query": "Show me high risk MSMEs",
            "expected_keywords": ["risk", "msme", "high", "alert", "score"],
            "min_length": 100
        },
        {
            "query": "What compliance deadlines are coming up?",
            "expected_keywords": ["compliance", "deadline", "filing", "regulatory"],
            "min_length": 80
        },
        {
            "query": "Analyze portfolio overview",
            "expected_keywords": ["portfolio", "overview", "total", "distribution"],
            "min_length": 150
        }
    ]
    
    results = []
    for test in quality_tests:
        try:
            payload = {"query": test["query"], "context": {}}
            response = requests.post(f"{BASE_URL}/api/copilot/ask", json=payload)
            
            if response.status_code == 200:
                data = response.json()
                response_text = data.get('response', '').lower()
                
                # Check length
                length_ok = len(response_text) >= test["min_length"]
                
                # Check keywords
                keywords_found = sum(1 for keyword in test["expected_keywords"] 
                                   if keyword in response_text)
                keywords_ok = keywords_found >= len(test["expected_keywords"]) * 0.6
                
                # Check structure
                has_structure = bool(data.get('data'))
                has_suggestions = bool(data.get('suggestions'))
                
                if length_ok and keywords_ok:
                    print(f"✅ Quality test: '{test['query'][:30]}...'")
                    print(f"   Length: {len(response_text)} chars (min: {test['min_length']})")
                    print(f"   Keywords: {keywords_found}/{len(test['expected_keywords'])}")
                    print(f"   Structured data: {has_structure}")
                    print(f"   Suggestions: {has_suggestions}")
                    results.append(True)
                else:
                    print(f"❌ Quality test: '{test['query'][:30]}...'")
                    print(f"   Length OK: {length_ok}, Keywords OK: {keywords_ok}")
                    results.append(False)
            else:
                print(f"❌ Quality test failed with status: {response.status_code}")
                results.append(False)
                
        except Exception as e:
            print(f"❌ Quality test exception: {e}")
            results.append(False)
    
    success_rate = sum(results) / len(results) * 100
    print(f"\n📊 Response quality success rate: {success_rate:.1f}%")
    return success_rate > 70

def main():
    """Run all tests"""
    print("🚀 Starting Credit Chakra AI Copilot functionality tests...\n")
    
    tests = [
        ("Health Check", test_health_check),
        ("Copilot Ask Endpoint", test_copilot_ask),
        ("Copilot Insights Endpoint", test_copilot_insights),
        ("Error Handling", test_error_handling),
        ("Response Quality", test_response_quality)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"Running: {test_name}")
        print('='*60)
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test '{test_name}' failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n{'='*60}")
    print("TEST SUMMARY")
    print('='*60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    overall_success = passed / len(results) * 100
    print(f"\n📊 Overall Success Rate: {overall_success:.1f}% ({passed}/{len(results)} tests passed)")
    
    if overall_success >= 80:
        print("🎉 AI Copilot functionality is working well!")
    elif overall_success >= 60:
        print("⚠️  AI Copilot has some issues that need attention")
    else:
        print("🚨 AI Copilot has significant issues that need immediate fixing")

if __name__ == "__main__":
    main()
