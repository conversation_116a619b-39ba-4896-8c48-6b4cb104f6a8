#!/usr/bin/env python3
"""
Script to populate test data for Credit Chakra
Creates the simulated MSME data as specified
"""
import sys
import os
from datetime import datetime, timedelta
import uuid

# Add current directory to path
sys.path.append(os.path.dirname(__file__))

from firebase.init import get_firestore_client
from services.health_score import simulate_msme_data, calculate_health_score

def create_test_msmes():
    """Create test MSME profiles with simulated data"""
    try:
        db = get_firestore_client()
        simulated_data = simulate_msme_data()
        
        for msme_data in simulated_data:
            msme_id = msme_data['msme_id']
            
            # Calculate health score for this MSME
            score_details = calculate_health_score(msme_data['signals'])
            
            # Create MSME profile
            msme_profile = {
                'msme_id': msme_id,
                'name': msme_data['name'],
                'business_type': msme_data['business_type'],
                'location': msme_data['location'],
                'created_at': (datetime.utcnow() - timedelta(days=30)).isoformat(),
                'score': score_details['score'],
                'risk_band': score_details['risk_band'],
                'tags': [msme_data['business_type'], 'test_data']
            }
            
            # Save MSME profile
            msme_ref = db.collection('msmes').document(msme_id)
            msme_ref.set(msme_profile)
            print(f"Created MSME: {msme_data['name']} (Score: {score_details['score']}, Risk: {score_details['risk_band']})")
            
            # Add signals
            for i, signal in enumerate(msme_data['signals']):
                signal_id = str(uuid.uuid4())
                
                # Add signal metadata
                signal_data = {
                    'signal_id': signal_id,
                    'msme_id': msme_id,
                    'source': signal['source'],
                    'value': signal['value'],
                    'timestamp': (datetime.utcnow() - timedelta(hours=i)).isoformat(),
                    'metadata': signal.get('metadata', {})
                }
                
                # Save signal
                signal_ref = msme_ref.collection('signals').document(signal_id)
                signal_ref.set(signal_data)
                print(f"  Added {signal['source']} signal")
            
            # Add a sample nudge if risk is not green
            if score_details['risk_band'] != 'green':
                nudge_id = str(uuid.uuid4())
                
                nudge_messages = {
                    'red': 'Urgent: Your business health score has dropped significantly. Please review your recent activities.',
                    'yellow': 'Alert: Your business health score needs attention. Consider improving your key metrics.'
                }
                
                nudge_data = {
                    'nudge_id': nudge_id,
                    'msme_id': msme_id,
                    'trigger_type': 'score_drop',
                    'message': nudge_messages.get(score_details['risk_band'], 'Your business metrics need attention.'),
                    'medium': 'email',
                    'sent_at': datetime.utcnow().isoformat(),
                    'status': 'sent',
                    'metadata': {'auto_generated': True, 'score': score_details['score']}
                }
                
                nudge_ref = msme_ref.collection('nudges').document(nudge_id)
                nudge_ref.set(nudge_data)
                print(f"  Added nudge for {score_details['risk_band']} risk")
        
        print(f"\nSuccessfully created {len(simulated_data)} test MSME profiles!")
        return True
        
    except Exception as e:
        print(f"Error creating test data: {e}")
        return False

def add_historical_signals():
    """Add some historical signals to make the data more realistic"""
    try:
        db = get_firestore_client()
        
        # Historical data for Arjun Kirana Store (showing GST decline)
        arjun_historical = [
            {'source': 'gst', 'value': 120000, 'days_ago': 30},  # Previous month - higher
            {'source': 'gst', 'value': 110000, 'days_ago': 20},  # Mid decline
            {'source': 'upi', 'value': {'transaction_count': 25, 'merchant_count': 4}, 'days_ago': 15},
            {'source': 'reviews', 'value': {'review_count': 35, 'average_rating': 4.1}, 'days_ago': 10}
        ]
        
        # Historical data for Kiran Tailors (showing review decline)
        kiran_historical = [
            {'source': 'reviews', 'value': {'review_count': 150, 'average_rating': 4.2}, 'days_ago': 25},
            {'source': 'reviews', 'value': {'review_count': 130, 'average_rating': 4.0}, 'days_ago': 15},
            {'source': 'gst', 'value': 98000, 'days_ago': 20},
            {'source': 'upi', 'value': {'transaction_count': 18, 'merchant_count': 3}, 'days_ago': 10}
        ]
        
        historical_data = {
            'arjun_kirana_001': arjun_historical,
            'kiran_tailors_002': kiran_historical
        }
        
        for msme_id, signals in historical_data.items():
            msme_ref = db.collection('msmes').document(msme_id)
            
            for signal in signals:
                signal_id = str(uuid.uuid4())
                
                signal_data = {
                    'signal_id': signal_id,
                    'msme_id': msme_id,
                    'source': signal['source'],
                    'value': signal['value'],
                    'timestamp': (datetime.utcnow() - timedelta(days=signal['days_ago'])).isoformat(),
                    'metadata': {'historical': True}
                }
                
                signal_ref = msme_ref.collection('signals').document(signal_id)
                signal_ref.set(signal_data)
            
            print(f"Added {len(signals)} historical signals for {msme_id}")
        
        print("Historical signals added successfully!")
        return True
        
    except Exception as e:
        print(f"Error adding historical signals: {e}")
        return False

def main():
    """Main function to populate all test data"""
    print("=== Credit Chakra Test Data Population ===\n")
    
    # Create test MSMEs
    print("Creating test MSME profiles...")
    if not create_test_msmes():
        print("Failed to create test MSMEs")
        return
    
    print()
    
    # Add historical signals
    print("Adding historical signals...")
    if not add_historical_signals():
        print("Failed to add historical signals")
        return
    
    print("\n=== Test data population completed! ===")
    print("\nYou can now test the endpoints:")
    print("- GET /dashboard/portfolio - View all MSMEs")
    print("- GET /msme/{msme_id}/score - Get health scores")
    print("- POST /msme/{msme_id}/signals - Add new signals")
    print("- POST /msme/{msme_id}/nudge - Send nudges")

if __name__ == "__main__":
    main()
