from fastapi import APIRouter, HTTPException, status, BackgroundTasks
from typing import List, Optional
from datetime import datetime, date, timedelta

from models.cash_flow import (
    CashFlowRequest, CashFlowResponse, CashFlowStatement, SeasonalAnalysis,
    CashFlowForecast, WorkingCapitalAnalysis, CashFlowAnalytics
)
from services.cash_flow_service import CashFlowService

router = APIRouter()
cash_flow_service = CashFlowService()

@router.post("/cash-flow/analyze", response_model=CashFlowResponse)
async def analyze_cash_flow(request: CashFlowRequest):
    """Analyze cash flow for an MSME"""
    try:
        # Validate date range
        if request.period_start > request.period_end:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="period_start cannot be greater than period_end"
            )
        
        # Generate cash flow statements
        cash_flow_statements = []
        current_date = request.period_start
        
        while current_date <= request.period_end:
            # Generate monthly statements
            if current_date.month == 12:
                month_end = date(current_date.year + 1, 1, 1) - timedelta(days=1)
            else:
                month_end = date(current_date.year, current_date.month + 1, 1) - timedelta(days=1)
            
            month_end = min(month_end, request.period_end)
            
            statement = await cash_flow_service.generate_cash_flow_statement(
                request.msme_id, current_date, month_end
            )
            if statement:
                cash_flow_statements.append(statement)
            
            # Move to next month
            if current_date.month == 12:
                current_date = date(current_date.year + 1, 1, 1)
            else:
                current_date = date(current_date.year, current_date.month + 1, 1)
        
        # Perform seasonal analysis if requested
        seasonal_analysis = None
        if request.include_seasonal:
            seasonal_analysis = await cash_flow_service.analyze_seasonal_patterns(
                request.msme_id, request.period_start.year
            )
        
        # Generate forecast if requested
        forecast = None
        if request.include_forecast:
            forecast = await cash_flow_service.generate_cash_flow_forecast(
                request.msme_id, request.forecast_horizon
            )
        
        # Analyze working capital if requested
        working_capital_analysis = None
        if request.include_working_capital:
            working_capital_analysis = await cash_flow_service.analyze_working_capital(
                request.msme_id, request.period_end
            )
        
        # Calculate comprehensive analytics
        analytics = await cash_flow_service.calculate_cash_flow_analytics(request.msme_id)
        
        return CashFlowResponse(
            msme_id=request.msme_id,
            cash_flow_statements=cash_flow_statements,
            seasonal_analysis=seasonal_analysis,
            forecast=forecast,
            working_capital_analysis=working_capital_analysis,
            analytics=analytics,
            status="success",
            message=f"Successfully analyzed cash flow for {len(cash_flow_statements)} periods"
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to analyze cash flow: {str(e)}"
        )

@router.get("/cash-flow/{msme_id}/statements", response_model=List[CashFlowStatement])
async def get_cash_flow_statements(msme_id: str, from_date: date, to_date: date):
    """Get cash flow statements for a period"""
    try:
        statements = []
        current_date = from_date
        
        while current_date <= to_date:
            # Generate monthly statements
            if current_date.month == 12:
                month_end = date(current_date.year + 1, 1, 1) - timedelta(days=1)
            else:
                month_end = date(current_date.year, current_date.month + 1, 1) - timedelta(days=1)
            
            month_end = min(month_end, to_date)
            
            statement = await cash_flow_service.generate_cash_flow_statement(
                msme_id, current_date, month_end
            )
            if statement:
                statements.append(statement)
            
            # Move to next month
            if current_date.month == 12:
                current_date = date(current_date.year + 1, 1, 1)
            else:
                current_date = date(current_date.year, current_date.month + 1, 1)
        
        return statements
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get cash flow statements: {str(e)}"
        )

@router.get("/cash-flow/{msme_id}/seasonal-analysis", response_model=SeasonalAnalysis)
async def get_seasonal_analysis(msme_id: str, year: int):
    """Get seasonal cash flow analysis"""
    try:
        analysis = await cash_flow_service.analyze_seasonal_patterns(msme_id, year)
        
        if not analysis:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Insufficient data for seasonal analysis. Need at least 6 months of data."
            )
        
        return analysis
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get seasonal analysis: {str(e)}"
        )

@router.get("/cash-flow/{msme_id}/forecast", response_model=CashFlowForecast)
async def get_cash_flow_forecast(msme_id: str, forecast_horizon: int = 12):
    """Get cash flow forecast"""
    try:
        if forecast_horizon < 1 or forecast_horizon > 24:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Forecast horizon must be between 1 and 24 months"
            )
        
        forecast = await cash_flow_service.generate_cash_flow_forecast(msme_id, forecast_horizon)
        
        if not forecast:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Insufficient historical data for forecasting. Need at least 3 months of data."
            )
        
        return forecast
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate cash flow forecast: {str(e)}"
        )

@router.get("/cash-flow/{msme_id}/working-capital", response_model=WorkingCapitalAnalysis)
async def get_working_capital_analysis(msme_id: str, analysis_date: Optional[date] = None):
    """Get working capital analysis"""
    try:
        if not analysis_date:
            analysis_date = date.today()
        
        analysis = await cash_flow_service.analyze_working_capital(msme_id, analysis_date)
        
        if not analysis:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Insufficient financial data for working capital analysis"
            )
        
        return analysis
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get working capital analysis: {str(e)}"
        )

@router.get("/cash-flow/{msme_id}/analytics", response_model=CashFlowAnalytics)
async def get_cash_flow_analytics(msme_id: str):
    """Get comprehensive cash flow analytics"""
    try:
        analytics = await cash_flow_service.calculate_cash_flow_analytics(msme_id)
        
        if not analytics:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No cash flow data found to calculate analytics"
            )
        
        return analytics
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get cash flow analytics: {str(e)}"
        )

@router.get("/cash-flow/{msme_id}/summary", response_model=dict)
async def get_cash_flow_summary(msme_id: str):
    """Get cash flow summary with key metrics"""
    try:
        # Get analytics
        analytics = await cash_flow_service.calculate_cash_flow_analytics(msme_id)
        
        # Get recent forecast
        forecast = await cash_flow_service.generate_cash_flow_forecast(msme_id, 6)
        
        # Get working capital analysis
        working_capital = await cash_flow_service.analyze_working_capital(msme_id, date.today())
        
        summary = {
            "msme_id": msme_id,
            "cash_flow_health": {
                "overall_score": analytics.cash_flow_health_score if analytics else 0,
                "liquidity_score": analytics.liquidity_score if analytics else 0,
                "efficiency_score": analytics.efficiency_score if analytics else 0,
                "quality_score": analytics.cash_flow_quality if analytics else 0
            } if analytics else None,
            "key_metrics": {
                "free_cash_flow": analytics.free_cash_flow if analytics else 0,
                "operating_cash_flow_ratio": analytics.operating_cash_flow_ratio if analytics else 0,
                "cash_flow_volatility": analytics.cash_flow_volatility if analytics else 0,
                "cash_flow_growth_rate": analytics.cash_flow_growth_rate if analytics else 0
            } if analytics else None,
            "working_capital": {
                "working_capital": working_capital.working_capital if working_capital else 0,
                "cash_conversion_cycle": working_capital.cash_conversion_cycle if working_capital else 0,
                "efficiency_score": working_capital.efficiency_score if working_capital else 0
            } if working_capital else None,
            "forecast_insights": {
                "next_6_months_net_flow": forecast.net_cash_flow_forecast if forecast else 0,
                "projected_gaps": len(forecast.projected_gaps) if forecast else 0,
                "surplus_periods": len(forecast.surplus_periods) if forecast else 0,
                "confidence_level": forecast.confidence_level if forecast else 0
            } if forecast else None,
            "risk_indicators": {
                "liquidity_risk": analytics.liquidity_risk_score if analytics else 0,
                "cash_flow_predictability": analytics.cash_flow_predictability if analytics else 0,
                "volatility_concern": analytics.cash_flow_volatility > 30 if analytics else False
            } if analytics else None,
            "recommendations": _generate_cash_flow_recommendations(analytics, working_capital, forecast),
            "last_updated": datetime.utcnow()
        }
        
        return summary
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get cash flow summary: {str(e)}"
        )

@router.post("/cash-flow/{msme_id}/refresh", status_code=status.HTTP_202_ACCEPTED)
async def refresh_cash_flow_analysis(msme_id: str, background_tasks: BackgroundTasks):
    """Trigger refresh of cash flow analysis"""
    try:
        background_tasks.add_task(_refresh_cash_flow_background, msme_id)
        
        return {
            "message": "Cash flow analysis refresh initiated",
            "msme_id": msme_id,
            "estimated_completion": "2-3 minutes"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to initiate cash flow refresh: {str(e)}"
        )

@router.get("/cash-flow/{msme_id}/gaps-and-surpluses", response_model=dict)
async def get_cash_flow_gaps_and_surpluses(msme_id: str, forecast_horizon: int = 12):
    """Get projected cash flow gaps and surpluses"""
    try:
        forecast = await cash_flow_service.generate_cash_flow_forecast(msme_id, forecast_horizon)
        
        if not forecast:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No forecast data available"
            )
        
        return {
            "msme_id": msme_id,
            "forecast_period": f"{forecast.forecast_start} to {forecast.forecast_end}",
            "projected_gaps": forecast.projected_gaps,
            "surplus_periods": forecast.surplus_periods,
            "total_gap_amount": sum(gap["gap_amount"] for gap in forecast.projected_gaps),
            "total_surplus_amount": sum(surplus["surplus_amount"] for surplus in forecast.surplus_periods),
            "net_position": sum(surplus["surplus_amount"] for surplus in forecast.surplus_periods) - 
                           sum(gap["gap_amount"] for gap in forecast.projected_gaps),
            "recommendations": _generate_gap_recommendations(forecast.projected_gaps, forecast.surplus_periods)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get gaps and surpluses: {str(e)}"
        )

# Helper functions
async def _refresh_cash_flow_background(msme_id: str):
    """Background task to refresh cash flow analysis"""
    try:
        # Refresh all cash flow components
        end_date = date.today()
        start_date = date(end_date.year - 1, end_date.month, 1)  # Last 12 months
        
        # Generate fresh statements
        current_date = start_date
        while current_date <= end_date:
            if current_date.month == 12:
                month_end = date(current_date.year + 1, 1, 1) - timedelta(days=1)
            else:
                month_end = date(current_date.year, current_date.month + 1, 1) - timedelta(days=1)
            
            month_end = min(month_end, end_date)
            
            await cash_flow_service.generate_cash_flow_statement(msme_id, current_date, month_end)
            
            # Move to next month
            if current_date.month == 12:
                current_date = date(current_date.year + 1, 1, 1)
            else:
                current_date = date(current_date.year, current_date.month + 1, 1)
        
        # Refresh analytics
        await cash_flow_service.calculate_cash_flow_analytics(msme_id)
        
        # Refresh seasonal analysis
        await cash_flow_service.analyze_seasonal_patterns(msme_id, end_date.year)
        
        # Refresh forecast
        await cash_flow_service.generate_cash_flow_forecast(msme_id, 12)
        
        # Refresh working capital analysis
        await cash_flow_service.analyze_working_capital(msme_id, end_date)
        
    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Cash flow refresh failed for {msme_id}: {str(e)}")

def _generate_cash_flow_recommendations(analytics, working_capital, forecast) -> List[str]:
    """Generate cash flow recommendations"""
    recommendations = []
    
    if analytics:
        if analytics.cash_flow_health_score < 60:
            recommendations.append("Improve cash flow management through better collection and payment timing")
        
        if analytics.cash_flow_volatility > 30:
            recommendations.append("Reduce cash flow volatility through diversification and better planning")
        
        if analytics.liquidity_score < 70:
            recommendations.append("Strengthen liquidity position by maintaining higher cash reserves")
    
    if working_capital:
        if working_capital.cash_conversion_cycle > 90:
            recommendations.append("Optimize working capital cycle by reducing DSO and inventory levels")
    
    if forecast:
        if len(forecast.projected_gaps) > 3:
            recommendations.append("Plan for upcoming cash flow gaps through credit facilities or timing adjustments")
        
        if forecast.confidence_level < 70:
            recommendations.append("Improve cash flow predictability through better forecasting and planning")
    
    return recommendations

def _generate_gap_recommendations(gaps, surpluses) -> List[str]:
    """Generate recommendations for gaps and surpluses"""
    recommendations = []
    
    if gaps:
        total_gap = sum(gap["gap_amount"] for gap in gaps)
        if total_gap > 100000:  # Significant gaps
            recommendations.append("Consider establishing a credit line to cover projected cash flow gaps")
            recommendations.append("Accelerate collections during gap periods")
            recommendations.append("Defer non-essential payments during cash flow gaps")
    
    if surpluses:
        total_surplus = sum(surplus["surplus_amount"] for surplus in surpluses)
        if total_surplus > 200000:  # Significant surpluses
            recommendations.append("Consider short-term investments during surplus periods")
            recommendations.append("Use surplus periods to prepay high-interest debt")
            recommendations.append("Build cash reserves for future gap periods")
    
    if len(gaps) > len(surpluses):
        recommendations.append("Focus on improving cash generation and collection efficiency")
    
    return recommendations
