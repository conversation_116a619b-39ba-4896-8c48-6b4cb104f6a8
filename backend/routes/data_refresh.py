from fastapi import APIRouter, HTTPException, status, BackgroundTasks
from typing import List, Optional
from datetime import datetime

from services.data_refresh_service import DataRefreshService, DataSource, RefreshPriority

router = APIRouter()
refresh_service = DataRefreshService()

@router.post("/data-refresh/schedule", status_code=status.HTTP_202_ACCEPTED)
async def schedule_data_refresh(
    msme_id: str,
    data_sources: List[str],
    priority: str = "MEDIUM"
):
    """Schedule data refresh for specific sources"""
    try:
        # Validate data sources
        valid_sources = []
        for source in data_sources:
            try:
                valid_sources.append(DataSource(source))
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Invalid data source: {source}"
                )
        
        # Validate priority
        try:
            refresh_priority = RefreshPriority(priority)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid priority: {priority}"
            )
        
        # Schedule refresh
        job_ids = await refresh_service.schedule_refresh(msme_id, valid_sources, refresh_priority)
        
        return {
            "message": "Data refresh scheduled successfully",
            "msme_id": msme_id,
            "job_ids": job_ids,
            "data_sources": data_sources,
            "priority": priority,
            "estimated_completion": "5-15 minutes"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to schedule data refresh: {str(e)}"
        )

@router.post("/data-refresh/comprehensive", status_code=status.HTTP_202_ACCEPTED)
async def schedule_comprehensive_refresh(
    msme_id: str,
    priority: str = "MEDIUM"
):
    """Schedule comprehensive refresh for all data sources"""
    try:
        # Validate priority
        try:
            refresh_priority = RefreshPriority(priority)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid priority: {priority}"
            )
        
        # Schedule comprehensive refresh
        job_ids = await refresh_service.schedule_comprehensive_refresh(msme_id, refresh_priority)
        
        return {
            "message": "Comprehensive data refresh scheduled successfully",
            "msme_id": msme_id,
            "job_ids": job_ids,
            "data_sources": ["GST", "ACCOUNT_AGGREGATOR", "UDYAM", "CASH_FLOW", "FINANCIAL_METRICS", "COMPLIANCE"],
            "priority": priority,
            "estimated_completion": "10-30 minutes"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to schedule comprehensive refresh: {str(e)}"
        )

@router.get("/data-refresh/job/{job_id}/status")
async def get_refresh_job_status(job_id: str):
    """Get status of a specific refresh job"""
    try:
        job = await refresh_service.get_refresh_status(job_id)
        
        if not job:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Refresh job {job_id} not found"
            )
        
        return {
            "job_id": job.job_id,
            "msme_id": job.msme_id,
            "data_source": job.data_source.value,
            "priority": job.priority.value,
            "status": job.status.value,
            "progress": job.progress,
            "created_at": job.created_at,
            "started_at": job.started_at,
            "completed_at": job.completed_at,
            "error_message": job.error_message,
            "retry_count": job.retry_count,
            "metadata": job.metadata
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get refresh job status: {str(e)}"
        )

@router.delete("/data-refresh/job/{job_id}")
async def cancel_refresh_job(job_id: str):
    """Cancel a refresh job"""
    try:
        success = await refresh_service.cancel_refresh_job(job_id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Refresh job {job_id} not found or cannot be cancelled"
            )
        
        return {
            "message": "Refresh job cancelled successfully",
            "job_id": job_id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to cancel refresh job: {str(e)}"
        )

@router.get("/data-refresh/msme/{msme_id}/status")
async def get_msme_refresh_status(msme_id: str):
    """Get comprehensive refresh status for an MSME"""
    try:
        status_data = await refresh_service.get_msme_refresh_status(msme_id)
        
        if "error" in status_data:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=status_data["error"]
            )
        
        return status_data
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get MSME refresh status: {str(e)}"
        )

@router.get("/data-refresh/system/health")
async def get_system_health():
    """Get overall system health and refresh statistics"""
    try:
        health_data = await refresh_service.get_system_health()
        
        if "error" in health_data:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=health_data["error"]
            )
        
        return health_data
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get system health: {str(e)}"
        )

@router.get("/data-refresh/queue/status")
async def get_queue_status():
    """Get current queue status"""
    try:
        health_data = await refresh_service.get_system_health()
        
        return {
            "queue_status": health_data.get("queue_status", {}),
            "system_status": health_data.get("system_status", "UNKNOWN"),
            "last_updated": datetime.utcnow()
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get queue status: {str(e)}"
        )

@router.post("/data-refresh/bulk-schedule", status_code=status.HTTP_202_ACCEPTED)
async def schedule_bulk_refresh(
    msme_ids: List[str],
    data_sources: List[str],
    priority: str = "LOW"
):
    """Schedule bulk refresh for multiple MSMEs"""
    try:
        if len(msme_ids) > 100:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot schedule refresh for more than 100 MSMEs at once"
            )
        
        # Validate data sources
        valid_sources = []
        for source in data_sources:
            try:
                valid_sources.append(DataSource(source))
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Invalid data source: {source}"
                )
        
        # Validate priority
        try:
            refresh_priority = RefreshPriority(priority)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid priority: {priority}"
            )
        
        # Schedule refresh for all MSMEs
        all_job_ids = []
        for msme_id in msme_ids:
            job_ids = await refresh_service.schedule_refresh(msme_id, valid_sources, refresh_priority)
            all_job_ids.extend(job_ids)
        
        return {
            "message": "Bulk data refresh scheduled successfully",
            "total_msmes": len(msme_ids),
            "total_jobs": len(all_job_ids),
            "job_ids": all_job_ids,
            "data_sources": data_sources,
            "priority": priority,
            "estimated_completion": f"{len(msme_ids) * 5}-{len(msme_ids) * 15} minutes"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to schedule bulk refresh: {str(e)}"
        )

@router.get("/data-refresh/sources")
async def get_available_data_sources():
    """Get list of available data sources for refresh"""
    return {
        "data_sources": [
            {
                "name": "GST",
                "description": "GST turnover and compliance data",
                "refresh_interval": "6 hours",
                "typical_duration": "2-3 minutes"
            },
            {
                "name": "ACCOUNT_AGGREGATOR",
                "description": "Banking and transaction data",
                "refresh_interval": "2 hours",
                "typical_duration": "3-5 minutes"
            },
            {
                "name": "UDYAM",
                "description": "Udyam registration and compliance data",
                "refresh_interval": "24 hours",
                "typical_duration": "1-2 minutes"
            },
            {
                "name": "CASH_FLOW",
                "description": "Cash flow analysis and forecasting",
                "refresh_interval": "4 hours",
                "typical_duration": "4-6 minutes"
            },
            {
                "name": "FINANCIAL_METRICS",
                "description": "Financial ratios and metrics",
                "refresh_interval": "8 hours",
                "typical_duration": "2-3 minutes"
            },
            {
                "name": "COMPLIANCE",
                "description": "Compliance and security assessment",
                "refresh_interval": "24 hours",
                "typical_duration": "1-2 minutes"
            }
        ],
        "priorities": [
            {
                "name": "CRITICAL",
                "description": "Highest priority, processed immediately"
            },
            {
                "name": "HIGH",
                "description": "High priority, processed within 5 minutes"
            },
            {
                "name": "MEDIUM",
                "description": "Normal priority, processed within 15 minutes"
            },
            {
                "name": "LOW",
                "description": "Low priority, processed when resources available"
            }
        ]
    }

@router.post("/data-refresh/webhook/notify", status_code=status.HTTP_200_OK)
async def webhook_notification(
    job_id: str,
    status: str,
    webhook_url: Optional[str] = None
):
    """Webhook endpoint for refresh job notifications"""
    try:
        # This would typically send notifications to external systems
        # For now, just log the notification
        import logging
        logger = logging.getLogger(__name__)
        logger.info(f"Webhook notification: Job {job_id} status changed to {status}")
        
        if webhook_url:
            # In production, would make HTTP request to webhook_url
            pass
        
        return {
            "message": "Webhook notification processed",
            "job_id": job_id,
            "status": status,
            "timestamp": datetime.utcnow()
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to process webhook notification: {str(e)}"
        )
