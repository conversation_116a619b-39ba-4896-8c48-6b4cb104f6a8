from fastapi import APIRouter, HTTPException, status, BackgroundTasks
from typing import List, Optional
from datetime import datetime, date

from models.account_aggregator import (
    ConsentRequest, AADataRequest, AADataResponse, AAHealthCheck,
    BankAccount, Transaction, CashFlowStatement, BankingAnalytics,
    ConsentStatus
)
from services.account_aggregator_service import AccountAggregatorService

router = APIRouter()
aa_service = AccountAggregatorService()

@router.post("/aa/consent/create", response_model=dict, status_code=status.HTTP_201_CREATED)
async def create_consent_request(consent_request: ConsentRequest):
    """Create a new consent request for Account Aggregator"""
    try:
        consent_id = await aa_service.create_consent_request(consent_request)
        
        return {
            "consent_id": consent_id,
            "status": "created",
            "consent_url": f"https://aa-consent-manager.com/consent/{consent_id}",
            "expires_at": consent_request.consent_expiry,
            "message": "Consent request created. User needs to approve through their bank."
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create consent request: {str(e)}"
        )

@router.get("/aa/consent/{consent_id}/status", response_model=dict)
async def get_consent_status(consent_id: str):
    """Get consent status"""
    try:
        status_result = await aa_service.check_consent_status(consent_id)
        
        return {
            "consent_id": consent_id,
            "status": status_result.value,
            "is_active": status_result == ConsentStatus.ACTIVE,
            "can_fetch_data": status_result == ConsentStatus.ACTIVE,
            "last_checked": datetime.utcnow()
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to check consent status: {str(e)}"
        )

@router.post("/aa/accounts/fetch", response_model=List[BankAccount])
async def fetch_bank_accounts(consent_id: str, customer_id: str):
    """Fetch bank accounts using Account Aggregator"""
    try:
        # Check consent status first
        consent_status = await aa_service.check_consent_status(consent_id)
        if consent_status != ConsentStatus.ACTIVE:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Consent not active. Current status: {consent_status.value}"
            )
        
        accounts = await aa_service.fetch_account_data(consent_id, customer_id)
        
        if not accounts:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No bank accounts found for the given consent"
            )
        
        return accounts
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch bank accounts: {str(e)}"
        )

@router.post("/aa/transactions/fetch", response_model=List[Transaction])
async def fetch_transactions(consent_id: str, account_id: str, 
                           from_date: date, to_date: date):
    """Fetch transactions for a specific account"""
    try:
        # Validate date range
        if from_date > to_date:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="from_date cannot be greater than to_date"
            )
        
        # Check if date range is not too large (max 1 year)
        if (to_date - from_date).days > 365:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Date range cannot exceed 365 days"
            )
        
        transactions = await aa_service.fetch_transaction_data(
            consent_id, account_id, from_date, to_date
        )
        
        return transactions
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch transactions: {str(e)}"
        )

@router.post("/aa/cash-flow/generate", response_model=CashFlowStatement)
async def generate_cash_flow_statement(account_id: str, period_start: date, period_end: date):
    """Generate cash flow statement for an account"""
    try:
        # Validate date range
        if period_start > period_end:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="period_start cannot be greater than period_end"
            )
        
        cash_flow = await aa_service.generate_cash_flow_statement(
            account_id, period_start, period_end
        )
        
        if not cash_flow:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No transaction data found for the specified period"
            )
        
        return cash_flow
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate cash flow statement: {str(e)}"
        )

@router.get("/aa/{consent_id}/analytics", response_model=BankingAnalytics)
async def get_banking_analytics(consent_id: str, msme_id: str):
    """Get comprehensive banking analytics"""
    try:
        analytics = await aa_service.calculate_banking_analytics(msme_id, consent_id)
        
        if not analytics:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No banking data found to calculate analytics"
            )
        
        return analytics
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to calculate banking analytics: {str(e)}"
        )

@router.get("/aa/{consent_id}/health", response_model=AAHealthCheck)
async def get_aa_health_check(consent_id: str, customer_id: str):
    """Get Account Aggregator data source health check"""
    try:
        health_check = await aa_service.get_aa_health_check(consent_id, customer_id)
        return health_check
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get AA health check: {str(e)}"
        )

@router.post("/aa/{consent_id}/refresh", status_code=status.HTTP_202_ACCEPTED)
async def refresh_aa_data(consent_id: str, customer_id: str, background_tasks: BackgroundTasks):
    """Trigger manual refresh of Account Aggregator data"""
    try:
        # Check consent status
        consent_status = await aa_service.check_consent_status(consent_id)
        if consent_status != ConsentStatus.ACTIVE:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Cannot refresh data. Consent status: {consent_status.value}"
            )
        
        # Schedule background refresh
        background_tasks.add_task(_refresh_aa_data_background, consent_id, customer_id)
        
        return {
            "message": "AA data refresh initiated",
            "consent_id": consent_id,
            "customer_id": customer_id,
            "estimated_completion": "2-5 minutes"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to initiate AA data refresh: {str(e)}"
        )

@router.get("/aa/{consent_id}/summary", response_model=dict)
async def get_aa_data_summary(consent_id: str, customer_id: str):
    """Get summary of Account Aggregator data"""
    try:
        # Get accounts
        accounts = await aa_service.fetch_account_data(consent_id, customer_id)
        
        # Get analytics
        analytics = await aa_service.calculate_banking_analytics(customer_id, consent_id)
        
        # Calculate summary metrics
        total_balance = sum(account.current_balance for account in accounts)
        active_accounts = len([account for account in accounts if account.is_active])
        
        summary = {
            "consent_id": consent_id,
            "customer_id": customer_id,
            "accounts_summary": {
                "total_accounts": len(accounts),
                "active_accounts": active_accounts,
                "total_balance": total_balance,
                "account_types": list(set([account.account_type.value for account in accounts]))
            },
            "banking_health": {
                "banking_score": analytics.banking_score if analytics else 0,
                "stability_score": analytics.stability_score if analytics else 0,
                "digital_maturity_score": analytics.digital_maturity_score if analytics else 0,
                "digital_transaction_ratio": analytics.digital_transaction_ratio if analytics else 0,
                "upi_adoption_rate": analytics.upi_adoption_rate if analytics else 0
            } if analytics else None,
            "risk_indicators": {
                "irregular_transaction_flag": analytics.irregular_transaction_flag if analytics else False,
                "cash_intensive_flag": analytics.cash_intensive_flag if analytics else False,
                "dormant_account_flag": analytics.dormant_account_flag if analytics else False
            } if analytics else None,
            "last_updated": datetime.utcnow()
        }
        
        return summary
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get AA data summary: {str(e)}"
        )

@router.get("/aa/{consent_id}/digital-insights", response_model=dict)
async def get_digital_insights(consent_id: str, customer_id: str):
    """Get digital payment insights from Account Aggregator data"""
    try:
        analytics = await aa_service.calculate_banking_analytics(customer_id, consent_id)
        
        if not analytics:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No banking analytics found"
            )
        
        insights = {
            "digital_adoption": {
                "digital_transaction_ratio": analytics.digital_transaction_ratio,
                "upi_adoption_rate": analytics.upi_adoption_rate,
                "digital_payment_growth": analytics.digital_payment_growth,
                "digital_maturity_score": analytics.digital_maturity_score
            },
            "transaction_patterns": {
                "total_transactions": analytics.total_transactions,
                "avg_monthly_transactions": analytics.avg_monthly_transactions,
                "avg_transaction_amount": analytics.avg_transaction_amount
            },
            "behavioral_insights": {
                "cash_intensive_behavior": analytics.cash_intensive_flag,
                "digital_trend": analytics.digital_trend,
                "account_utilization_rate": analytics.account_utilization_rate
            },
            "recommendations": _generate_digital_recommendations(analytics),
            "benchmarks": {
                "industry_digital_ratio": 75.0,  # Mock benchmark
                "industry_upi_adoption": 60.0,   # Mock benchmark
                "target_digital_score": 85.0     # Mock target
            }
        }
        
        return insights
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get digital insights: {str(e)}"
        )

# Helper functions
async def _refresh_aa_data_background(consent_id: str, customer_id: str):
    """Background task to refresh Account Aggregator data"""
    try:
        # Fetch fresh account data
        accounts = await aa_service.fetch_account_data(consent_id, customer_id)
        
        # Fetch recent transactions for each account
        from datetime import timedelta
        end_date = date.today()
        start_date = end_date - timedelta(days=90)  # Last 3 months
        
        for account in accounts:
            await aa_service.fetch_transaction_data(
                consent_id, account.account_id, start_date, end_date
            )
        
        # Recalculate analytics
        await aa_service.calculate_banking_analytics(customer_id, consent_id)
        
    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Background AA refresh failed for {consent_id}: {str(e)}")

def _generate_digital_recommendations(analytics: BankingAnalytics) -> List[str]:
    """Generate digital adoption recommendations"""
    recommendations = []
    
    if analytics.digital_transaction_ratio < 70:
        recommendations.append("Increase digital payment adoption to improve efficiency")
    
    if analytics.upi_adoption_rate < 50:
        recommendations.append("Adopt UPI payments for faster and cheaper transactions")
    
    if analytics.cash_intensive_flag:
        recommendations.append("Reduce cash-intensive transactions to improve transparency")
    
    if analytics.digital_payment_growth < 10:
        recommendations.append("Focus on growing digital payment usage")
    
    if analytics.account_utilization_rate < 60:
        recommendations.append("Optimize account usage across different banks")
    
    return recommendations
