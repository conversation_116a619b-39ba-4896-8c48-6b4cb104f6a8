from fastapi import APIRouter, HTTPException, status
from typing import List, Optional, Dict, Any
from datetime import datetime
import uuid
from pydantic import BaseModel

from models.nudge import Nudge, NudgeCreate, NudgeUpdate, NudgeStatus, NudgeRequest
from firebase.init import get_firestore_client

router = APIRouter()

# Bulk nudge models
class BulkNudgeRequest(BaseModel):
    msme_ids: List[str]
    trigger_type: str
    message: str
    medium: str
    metadata: Optional[Dict[str, Any]] = None

class BulkNudgeResponse(BaseModel):
    total_requested: int
    successful: int
    failed: int
    nudge_ids: List[str]
    errors: List[Dict[str, str]]

# Bulk nudge endpoint
@router.post("/bulk", response_model=BulkNudgeResponse, status_code=status.HTTP_201_CREATED)
async def send_bulk_nudges(bulk_request: BulkNudgeRequest):
    """Send nudges to multiple MSMEs"""
    try:
        db = get_firestore_client()

        successful_nudges = []
        failed_nudges = []
        errors = []

        for msme_id in bulk_request.msme_ids:
            try:
                # Verify MSME exists
                msme_ref = db.collection('msmes').document(msme_id)
                msme_doc = msme_ref.get()

                if not msme_doc.exists:
                    errors.append({
                        "msme_id": msme_id,
                        "error": "MSME profile not found"
                    })
                    failed_nudges.append(msme_id)
                    continue

                # Generate unique nudge ID
                nudge_id = str(uuid.uuid4())

                # Create nudge data
                nudge_data = {
                    'nudge_id': nudge_id,
                    'msme_id': msme_id,
                    'trigger_type': bulk_request.trigger_type,
                    'message': bulk_request.message,
                    'medium': bulk_request.medium,
                    'sent_at': datetime.utcnow().isoformat(),
                    'status': 'sent',
                    'metadata': bulk_request.metadata or {}
                }

                # Save nudge to Firestore
                nudge_ref = db.collection('msmes').document(msme_id).collection('nudges').document(nudge_id)
                nudge_ref.set(nudge_data)

                successful_nudges.append(nudge_id)

            except Exception as e:
                errors.append({
                    "msme_id": msme_id,
                    "error": str(e)
                })
                failed_nudges.append(msme_id)

        return BulkNudgeResponse(
            total_requested=len(bulk_request.msme_ids),
            successful=len(successful_nudges),
            failed=len(failed_nudges),
            nudge_ids=successful_nudges,
            errors=errors
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to send bulk nudges: {str(e)}"
        )

# New MSME-centric endpoints for Day 3
@router.post("/msme/{msme_id}/nudges", response_model=Nudge, status_code=status.HTTP_201_CREATED)
async def send_nudge_to_msme(msme_id: str, nudge_request: NudgeRequest):
    """Send a nudge to an MSME"""
    try:
        db = get_firestore_client()

        # Verify MSME exists
        msme_ref = db.collection('msmes').document(msme_id)
        msme_doc = msme_ref.get()

        if not msme_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="MSME profile not found"
            )

        # Generate unique nudge ID
        nudge_id = str(uuid.uuid4())

        # Create nudge
        nudge_data = {
            'nudge_id': nudge_id,
            'msme_id': msme_id,
            'trigger_type': nudge_request.trigger_type,
            'message': nudge_request.message,
            'medium': nudge_request.medium,
            'sent_at': datetime.utcnow().isoformat(),
            'status': 'sent',  # For now, assume immediate sending
            'metadata': nudge_request.metadata
        }

        # Save nudge to Firestore
        nudge_ref = db.collection('msmes').document(msme_id).collection('nudges').document(nudge_id)
        nudge_ref.set(nudge_data)

        # Return created nudge
        return Nudge(**nudge_data)

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to send nudge: {str(e)}"
        )

@router.get("/msme/{msme_id}/nudges", response_model=List[dict])
async def get_msme_nudges(msme_id: str, limit: Optional[int] = 20):
    """Get nudges for an MSME ordered by sent_at descending"""
    try:
        db = get_firestore_client()

        # Verify MSME exists
        msme_ref = db.collection('msmes').document(msme_id)
        msme_doc = msme_ref.get()

        if not msme_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="MSME profile not found"
            )

        # Get nudges ordered by sent_at descending
        nudges_ref = msme_ref.collection('nudges').order_by('sent_at', direction='DESCENDING')

        if limit:
            nudges_ref = nudges_ref.limit(limit)

        nudges_docs = nudges_ref.stream()

        nudges = []
        for doc in nudges_docs:
            nudge_data = doc.to_dict()
            nudges.append(nudge_data)

        return nudges

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch nudges: {str(e)}"
        )

# Existing endpoints for backward compatibility
@router.post("/send", response_model=Nudge, status_code=status.HTTP_201_CREATED)
async def send_nudge(nudge_data: NudgeCreate):
    """Send a nudge to an MSME"""
    try:
        db = get_firestore_client()
        
        # Verify MSME exists
        msme_ref = db.collection('msmes').document(nudge_data.msme_id)
        msme_doc = msme_ref.get()
        
        if not msme_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="MSME profile not found"
            )
        
        # Generate unique nudge ID
        nudge_id = str(uuid.uuid4())
        
        # Create nudge
        nudge = Nudge(
            nudge_id=nudge_id,
            msme_id=nudge_data.msme_id,
            trigger_type=nudge_data.trigger_type,
            message=nudge_data.message,
            medium=nudge_data.medium,
            sent_at=datetime.utcnow(),
            status=NudgeStatus.SENT,  # For now, assume immediate sending
            metadata=nudge_data.metadata
        )
        
        # Save nudge to Firestore
        nudge_ref = db.collection('msmes').document(nudge_data.msme_id).collection('nudges').document(nudge_id)
        nudge_ref.set(nudge.dict())
        
        # TODO: Implement actual sending logic based on medium
        # For now, just mark as sent
        
        return nudge
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to send nudge: {str(e)}"
        )

@router.get("/{msme_id}", response_model=List[Nudge])
async def get_nudges(msme_id: str, status_filter: Optional[str] = None, limit: int = 50):
    """Get nudges for an MSME"""
    try:
        db = get_firestore_client()
        
        # Verify MSME exists
        msme_ref = db.collection('msmes').document(msme_id)
        msme_doc = msme_ref.get()
        
        if not msme_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="MSME profile not found"
            )
        
        # Build query
        query = db.collection('msmes').document(msme_id).collection('nudges')
        
        if status_filter:
            query = query.where('status', '==', status_filter)
        
        query = query.order_by('sent_at', direction='DESCENDING').limit(limit)
        
        # Execute query
        docs = query.stream()
        
        nudges = []
        for doc in docs:
            data = doc.to_dict()
            nudges.append(Nudge(**data))
        
        return nudges
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve nudges: {str(e)}"
        )

@router.put("/{msme_id}/{nudge_id}", response_model=Nudge)
async def update_nudge(msme_id: str, nudge_id: str, update_data: NudgeUpdate):
    """Update nudge status or content"""
    try:
        db = get_firestore_client()
        
        nudge_ref = db.collection('msmes').document(msme_id).collection('nudges').document(nudge_id)
        nudge_doc = nudge_ref.get()
        
        if not nudge_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Nudge not found"
            )
        
        # Update only provided fields
        update_dict = {k: v for k, v in update_data.dict().items() if v is not None}
        nudge_ref.update(update_dict)
        
        # Return updated nudge
        updated_doc = nudge_ref.get()
        data = updated_doc.to_dict()
        return Nudge(**data)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update nudge: {str(e)}"
        )
