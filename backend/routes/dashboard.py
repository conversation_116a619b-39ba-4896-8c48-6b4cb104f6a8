from fastapi import APIRouter, HTTPException, status
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from pydantic import BaseModel

from firebase.init import get_firestore_client
from services.health_score import calculate_health_score

router = APIRouter()

@router.get("/portfolio", response_model=List[Dict[str, Any]])
async def get_portfolio_summary():
    """Get portfolio summary with all MSMEs, scores, and risk status"""
    try:
        db = get_firestore_client()
        
        # Get all MSMEs
        msmes_ref = db.collection('msmes')
        msmes_docs = msmes_ref.stream()
        
        portfolio = []
        
        for msme_doc in msmes_docs:
            msme_data = msme_doc.to_dict()
            msme_id = msme_doc.id
            
            # Get latest signals for this MSME
            signals_ref = db.collection('msmes').document(msme_id).collection('signals')
            signals_docs = signals_ref.order_by('timestamp', direction='DESCENDING').limit(10).stream()
            
            signals = []
            for signal_doc in signals_docs:
                signals.append(signal_doc.to_dict())
            
            # Use stored score if available, otherwise calculate
            stored_score = msme_data.get('score', 0)
            stored_risk_band = msme_data.get('risk_band', 'red')

            if stored_score > 0:
                current_score = stored_score
                risk_band = stored_risk_band
            else:
                # Fallback to calculation if no stored score
                score_details = calculate_health_score(signals)
                current_score = score_details['score']
                risk_band = score_details['risk_band']

            # Get recent nudges count
            nudges_ref = db.collection('msmes').document(msme_id).collection('nudges')
            recent_nudges = nudges_ref.where(
                'sent_at', '>=', (datetime.utcnow() - timedelta(days=7)).isoformat()
            ).stream()

            recent_nudges_count = len(list(recent_nudges))

            # Build portfolio entry
            portfolio_entry = {
                'msme_id': msme_id,
                'name': msme_data.get('name', 'Unknown'),
                'business_type': msme_data.get('business_type', 'unknown'),
                'location': msme_data.get('location', 'Unknown'),
                'current_score': current_score,
                'risk_band': risk_band,
                'score_trend': _calculate_score_trend(signals),
                'signals_count': len(signals),
                'recent_nudges': recent_nudges_count,
                'last_signal_date': signals[0].get('timestamp') if signals else None,
                'created_at': msme_data.get('created_at'),
                'tags': msme_data.get('tags', [])
            }
            
            portfolio.append(portfolio_entry)
        
        # Sort by risk band (red first) and then by score
        risk_order = {'red': 0, 'yellow': 1, 'green': 2}
        portfolio.sort(key=lambda x: (risk_order.get(x['risk_band'], 3), -x['current_score']))
        
        return portfolio
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get portfolio summary: {str(e)}"
        )

def _calculate_score_trend(signals: List[Dict[str, Any]]) -> str:
    """Calculate score trend based on recent signals"""
    if len(signals) < 2:
        return "stable"
    
    # Get scores from last two signal periods
    recent_signals = signals[:5]  # Last 5 signals
    older_signals = signals[5:10] if len(signals) > 5 else []
    
    if not older_signals:
        return "stable"
    
    recent_score = calculate_health_score(recent_signals)['score']
    older_score = calculate_health_score(older_signals)['score']
    
    score_diff = recent_score - older_score
    
    if score_diff > 5:
        return "improving"
    elif score_diff < -5:
        return "declining"
    else:
        return "stable"

@router.get("/analytics", response_model=Dict[str, Any])
async def get_dashboard_analytics():
    """Get dashboard analytics and summary statistics"""
    try:
        db = get_firestore_client()
        
        # Get all MSMEs
        msmes_ref = db.collection('msmes')
        msmes_docs = list(msmes_ref.stream())
        
        total_msmes = len(msmes_docs)
        risk_distribution = {'green': 0, 'yellow': 0, 'red': 0}
        business_type_distribution = {}
        total_signals = 0
        
        for msme_doc in msmes_docs:
            msme_data = msme_doc.to_dict()
            msme_id = msme_doc.id

            # Get signals count
            signals_ref = db.collection('msmes').document(msme_id).collection('signals')
            signals_count = len(list(signals_ref.stream()))
            total_signals += signals_count

            # Use stored risk band if available, otherwise calculate
            stored_score = msme_data.get('score', 0)
            stored_risk_band = msme_data.get('risk_band', 'red')

            if stored_score > 0:
                risk_band = stored_risk_band
            else:
                # Fallback to calculation if no stored score
                signals_docs = signals_ref.order_by('timestamp', direction='DESCENDING').limit(10).stream()
                signals = [doc.to_dict() for doc in signals_docs]
                score_details = calculate_health_score(signals)
                risk_band = score_details['risk_band']

            # Update risk distribution
            risk_distribution[risk_band] = risk_distribution.get(risk_band, 0) + 1

            # Update business type distribution
            business_type = msme_data.get('business_type', 'unknown')
            business_type_distribution[business_type] = business_type_distribution.get(business_type, 0) + 1
        
        return {
            'total_msmes': total_msmes,
            'total_signals': total_signals,
            'risk_distribution': risk_distribution,
            'business_type_distribution': business_type_distribution,
            'average_signals_per_msme': round(total_signals / total_msmes, 2) if total_msmes > 0 else 0,
            'last_updated': datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get dashboard analytics: {str(e)}"
        )

# Enhanced analytics models
class ScoreTrendData(BaseModel):
    period: str
    average_score: float
    high_risk_count: int
    medium_risk_count: int
    low_risk_count: int

class SignalPatternData(BaseModel):
    source: str
    total_signals: int
    avg_per_msme: float
    trend: str
    last_7_days: int

class NudgeMetricsData(BaseModel):
    total_sent: int
    delivery_rate: float
    response_rate: float
    effectiveness_score: float
    by_medium: Dict[str, Dict[str, int]]

class BusinessPerformanceData(BaseModel):
    business_type: str
    avg_score: float
    msme_count: int
    risk_distribution: Dict[str, int]
    trend: str

@router.get("/analytics/trends", response_model=List[ScoreTrendData])
async def get_score_trends(period: Optional[str] = "30d"):
    """Get score trends over time"""
    try:
        # Mock data for now - in production, this would query historical data
        trends = []
        days = 30 if period == "30d" else 7 if period == "7d" else 90

        for i in range(days):
            date = datetime.utcnow() - timedelta(days=days-i-1)
            trends.append(ScoreTrendData(
                period=date.strftime("%Y-%m-%d"),
                average_score=650 + (i * 2) + ((-1) ** i * 10),
                high_risk_count=3 + (i % 3),
                medium_risk_count=8 + (i % 5),
                low_risk_count=12 + (i % 4)
            ))

        return trends

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get score trends: {str(e)}"
        )

@router.get("/analytics/signals", response_model=List[SignalPatternData])
async def get_signal_patterns():
    """Get signal ingestion patterns"""
    try:
        db = get_firestore_client()

        # Get all signals across all MSMEs
        signal_sources = ['gst', 'upi', 'reviews', 'instagram', 'justdial']
        patterns = []

        for source in signal_sources:
            # In a real implementation, this would query the database
            # For now, return mock data
            patterns.append(SignalPatternData(
                source=source,
                total_signals=100 + hash(source) % 100,
                avg_per_msme=5.0 + hash(source) % 5,
                trend="increasing" if hash(source) % 3 == 0 else "stable",
                last_7_days=10 + hash(source) % 15
            ))

        return patterns

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get signal patterns: {str(e)}"
        )

@router.get("/analytics/nudges", response_model=NudgeMetricsData)
async def get_nudge_metrics():
    """Get nudge effectiveness metrics"""
    try:
        db = get_firestore_client()

        # In a real implementation, this would aggregate nudge data
        # For now, return mock data
        return NudgeMetricsData(
            total_sent=234,
            delivery_rate=94.5,
            response_rate=23.8,
            effectiveness_score=78.2,
            by_medium={
                "whatsapp": {"sent": 145, "delivered": 142, "responded": 38},
                "email": {"sent": 67, "delivered": 61, "responded": 12},
                "sms": {"sent": 22, "delivered": 19, "responded": 6}
            }
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get nudge metrics: {str(e)}"
        )

@router.get("/analytics/business-performance", response_model=List[BusinessPerformanceData])
async def get_business_performance():
    """Get business type performance analysis"""
    try:
        db = get_firestore_client()

        # Get all MSMEs grouped by business type
        msmes_ref = db.collection('msmes')
        msmes_docs = msmes_ref.stream()

        business_data = {}

        for msme_doc in msmes_docs:
            msme_data = msme_doc.to_dict()
            business_type = msme_data.get('business_type', 'unknown')
            score = msme_data.get('score', 0)
            risk_band = msme_data.get('risk_band', 'red')

            if business_type not in business_data:
                business_data[business_type] = {
                    'scores': [],
                    'risk_distribution': {'green': 0, 'yellow': 0, 'red': 0}
                }

            business_data[business_type]['scores'].append(score)
            business_data[business_type]['risk_distribution'][risk_band] += 1

        performance = []
        for business_type, data in business_data.items():
            if data['scores']:
                avg_score = sum(data['scores']) / len(data['scores'])
                msme_count = len(data['scores'])

                performance.append(BusinessPerformanceData(
                    business_type=business_type,
                    avg_score=round(avg_score, 1),
                    msme_count=msme_count,
                    risk_distribution=data['risk_distribution'],
                    trend="improving" if avg_score > 600 else "stable"
                ))

        return performance

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get business performance: {str(e)}"
        )
