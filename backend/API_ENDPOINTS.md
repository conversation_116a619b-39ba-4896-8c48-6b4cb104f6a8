# Credit Chakra API Endpoints

## Overview

The Credit Chakra API provides endpoints for MSME credit scoring, signal management, and portfolio monitoring.

## Base URL
```
http://localhost:8000  (Development)
https://your-cloud-run-url  (Production)
```

## Health Check

### GET /health
Returns API health status.

**Response:**
```json
{
  "status": "healthy",
  "service": "credit-chakra-backend"
}
```

## MSME Management

### POST /msme/
Create a new MSME profile.

**Request Body:**
```json
{
  "name": "Arjun Kirana Store",
  "business_type": "retail",
  "location": "Hyderabad",
  "tags": ["retail", "grocery"]
}
```

**Response:**
```json
{
  "msme_id": "uuid-string",
  "name": "Arjun Kirana Store",
  "business_type": "retail",
  "location": "Hyderabad",
  "created_at": "2024-01-20T10:00:00Z",
  "score": 0.0,
  "risk_band": "red",
  "tags": ["retail", "grocery"]
}
```

### GET /msme/{id}
Fetch MSME profile with basic information.

**Response:**
```json
{
  "msme_id": "uuid-string",
  "name": "Arjun Kirana Store",
  "business_type": "retail",
  "location": "Hyderabad",
  "score": 65,
  "risk_band": "yellow",
  "tags": ["retail", "grocery"]
}
```

### POST /msme/{id}/signals
Add new signal (GST, UPI, Reviews, etc.) to MSME.

**Request Body:**
```json
{
  "source": "gst",
  "value": 80000,
  "metadata": {
    "month": "2024-01",
    "verified": true,
    "previous_value": 120000
  }
}
```

**Response:**
```json
{
  "signal_id": "uuid-string",
  "msme_id": "uuid-string",
  "signal_data": {
    "source": "gst",
    "value": 80000,
    "timestamp": "2024-01-20T14:30:00Z"
  },
  "score_update": {
    "msme_id": "uuid-string",
    "new_score": 65,
    "new_risk_band": "yellow",
    "signals_count": 3
  }
}
```

### GET /msme/{id}/score
Return calculated health score with breakdown.

**Response:**
```json
{
  "msme_id": "uuid-string",
  "msme_name": "Arjun Kirana Store",
  "current_score": 65,
  "risk_band": "yellow",
  "score_breakdown": {
    "base_score": 100,
    "gst_penalty": 20,
    "reviews_penalty": 0,
    "upi_penalty": 15,
    "details": {
      "gst": "GST drop of 33.3% detected",
      "upi": "Low UPI diversity: 2 merchants"
    }
  },
  "signals_count": 3,
  "last_updated": "2024-01-20T15:00:00Z"
}
```

### POST /msme/{id}/nudge
Trigger and log nudge message.

**Request Body:**
```json
{
  "message": "Your GST turnover has dropped significantly. Please review.",
  "trigger_type": "score_drop",
  "medium": "email",
  "metadata": {
    "campaign": "health_alert"
  }
}
```

**Response:**
```json
{
  "nudge_id": "uuid-string",
  "msme_id": "uuid-string",
  "message": "Your GST turnover has dropped significantly. Please review.",
  "status": "sent",
  "sent_at": "2024-01-20T15:30:00Z"
}
```

## Dashboard

### GET /dashboard/portfolio
List MSMEs with score & risk status summary.

**Response:**
```json
[
  {
    "msme_id": "uuid-string",
    "name": "Arjun Kirana Store",
    "business_type": "retail",
    "location": "Hyderabad",
    "current_score": 65,
    "risk_band": "yellow",
    "score_trend": "declining",
    "signals_count": 5,
    "recent_nudges": 2,
    "last_signal_date": "2024-01-20T14:30:00Z",
    "created_at": "2024-01-15T10:00:00Z",
    "tags": ["retail", "grocery"]
  }
]
```

### GET /dashboard/analytics
Get dashboard analytics and summary statistics.

**Response:**
```json
{
  "total_msmes": 15,
  "total_signals": 87,
  "risk_distribution": {
    "green": 5,
    "yellow": 7,
    "red": 3
  },
  "business_type_distribution": {
    "retail": 8,
    "services": 4,
    "b2b": 3
  },
  "average_signals_per_msme": 5.8,
  "last_updated": "2024-01-20T16:00:00Z"
}
```

## Signal Types

### GST Signals
```json
{
  "source": "gst",
  "value": 120000,  // Monthly turnover in rupees
  "metadata": {
    "month": "2024-01",
    "verified": true,
    "previous_value": 150000
  }
}
```

### UPI Signals
```json
{
  "source": "upi",
  "value": {
    "transaction_count": 25,
    "merchant_count": 4,
    "volume": 45000
  },
  "metadata": {
    "period": "weekly",
    "merchant_diversity": "medium"
  }
}
```

### Reviews Signals
```json
{
  "source": "reviews",
  "value": {
    "review_count": 90,
    "average_rating": 3.8
  },
  "metadata": {
    "platform": "google_maps",
    "previous_count": 150
  }
}
```

## Health Score Algorithm v0.1

**Base Score:** 100 points

**Penalties:**
- GST drop > 20%: -20 points
- Google review drop > 30%: -15 points  
- UPI merchant count < 3: -10 points

**Risk Bands:**
- Green: 70-100 points
- Yellow: 40-69 points
- Red: 0-39 points

## Error Responses

### 404 Not Found
```json
{
  "detail": "MSME profile not found"
}
```

### 500 Internal Server Error
```json
{
  "detail": "Failed to create MSME profile: Database connection error"
}
```

## Testing

Use the provided test script:
```bash
python test_api.py
```

Or populate with simulated data:
```bash
python populate_test_data.py
```

## Interactive Documentation

- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`
