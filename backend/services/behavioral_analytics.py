"""
Behavioral Analytics Engine
Advanced customer behavior analysis and pattern recognition
"""

import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import logging
import asyncio
from collections import defaultdict

logger = logging.getLogger(__name__)

class BehaviorPattern(Enum):
    PAYMENT_REGULARITY = "payment_regularity"
    BUSINESS_SEASONALITY = "business_seasonality"
    CASH_FLOW_VOLATILITY = "cash_flow_volatility"
    TRANSACTION_FREQUENCY = "transaction_frequency"
    BANKING_BEHAVIOR = "banking_behavior"
    COMPLIANCE_ADHERENCE = "compliance_adherence"
    GROWTH_TRAJECTORY = "growth_trajectory"
    STRESS_INDICATORS = "stress_indicators"

@dataclass
class BehaviorInsight:
    pattern_type: BehaviorPattern
    confidence_score: float
    trend_direction: str
    impact_on_risk: str
    description: str
    recommendations: List[str]
    supporting_data: Dict[str, Any]

@dataclass
class CustomerLifecycleStage:
    stage: str
    duration_in_stage: int
    probability_of_transition: Dict[str, float]
    expected_behaviors: List[str]
    risk_factors: List[str]
    opportunities: List[str]

@dataclass
class BehaviorProfile:
    msme_id: str
    behavior_score: float
    lifecycle_stage: CustomerLifecycleStage
    behavior_insights: List[BehaviorInsight]
    risk_indicators: Dict[str, float]
    opportunity_indicators: Dict[str, float]
    behavioral_trends: Dict[str, List[float]]
    last_updated: datetime

class BehavioralAnalyticsEngine:
    """
    Advanced behavioral analytics for customer insights and risk prediction
    """
    
    def __init__(self):
        self.behavior_models = {}
        self.pattern_cache = {}
        self.lifecycle_definitions = self._initialize_lifecycle_stages()
        self.behavior_weights = self._initialize_behavior_weights()
        
    def _initialize_lifecycle_stages(self) -> Dict[str, Dict[str, Any]]:
        """Initialize customer lifecycle stage definitions"""
        return {
            "new_customer": {
                "duration_range": (0, 12),  # 0-12 months
                "typical_behaviors": [
                    "frequent_inquiries",
                    "irregular_payments",
                    "high_utilization",
                    "documentation_delays"
                ],
                "risk_factors": [
                    "limited_credit_history",
                    "business_establishment_risk",
                    "cash_flow_uncertainty"
                ],
                "opportunities": [
                    "relationship_building",
                    "product_education",
                    "cross_selling_potential"
                ]
            },
            "growing_customer": {
                "duration_range": (12, 36),  # 1-3 years
                "typical_behaviors": [
                    "increasing_turnover",
                    "regular_payments",
                    "expanding_operations",
                    "seeking_additional_products"
                ],
                "risk_factors": [
                    "growth_financing_needs",
                    "operational_scaling_challenges",
                    "market_competition"
                ],
                "opportunities": [
                    "credit_limit_enhancement",
                    "product_diversification",
                    "preferential_pricing"
                ]
            },
            "mature_customer": {
                "duration_range": (36, 120),  # 3-10 years
                "typical_behaviors": [
                    "stable_payment_patterns",
                    "predictable_cash_flows",
                    "established_operations",
                    "compliance_adherence"
                ],
                "risk_factors": [
                    "market_saturation",
                    "technology_disruption",
                    "succession_planning"
                ],
                "opportunities": [
                    "relationship_deepening",
                    "advisory_services",
                    "ecosystem_partnerships"
                ]
            },
            "declining_customer": {
                "duration_range": (0, 999),  # Any duration
                "typical_behaviors": [
                    "irregular_payments",
                    "declining_turnover",
                    "reduced_engagement",
                    "compliance_issues"
                ],
                "risk_factors": [
                    "business_viability",
                    "management_issues",
                    "market_challenges",
                    "financial_distress"
                ],
                "opportunities": [
                    "restructuring_support",
                    "turnaround_assistance",
                    "exit_strategy_planning"
                ]
            }
        }
    
    def _initialize_behavior_weights(self) -> Dict[BehaviorPattern, float]:
        """Initialize weights for different behavior patterns"""
        return {
            BehaviorPattern.PAYMENT_REGULARITY: 0.25,
            BehaviorPattern.CASH_FLOW_VOLATILITY: 0.20,
            BehaviorPattern.BUSINESS_SEASONALITY: 0.15,
            BehaviorPattern.BANKING_BEHAVIOR: 0.15,
            BehaviorPattern.COMPLIANCE_ADHERENCE: 0.10,
            BehaviorPattern.TRANSACTION_FREQUENCY: 0.08,
            BehaviorPattern.GROWTH_TRAJECTORY: 0.07
        }
    
    async def analyze_customer_behavior(self, msme_data: Dict[str, Any], 
                                      transaction_history: List[Dict[str, Any]]) -> BehaviorProfile:
        """
        Comprehensive behavioral analysis for a customer
        """
        try:
            msme_id = msme_data.get('id', 'unknown')
            
            # Analyze individual behavior patterns
            behavior_insights = []
            
            # Payment behavior analysis
            payment_insight = await self._analyze_payment_behavior(transaction_history)
            behavior_insights.append(payment_insight)
            
            # Cash flow analysis
            cashflow_insight = await self._analyze_cash_flow_patterns(transaction_history)
            behavior_insights.append(cashflow_insight)
            
            # Banking behavior analysis
            banking_insight = await self._analyze_banking_behavior(transaction_history)
            behavior_insights.append(banking_insight)
            
            # Business seasonality analysis
            seasonality_insight = await self._analyze_business_seasonality(transaction_history)
            behavior_insights.append(seasonality_insight)
            
            # Compliance behavior analysis
            compliance_insight = await self._analyze_compliance_behavior(msme_data)
            behavior_insights.append(compliance_insight)
            
            # Calculate overall behavior score
            behavior_score = self._calculate_behavior_score(behavior_insights)
            
            # Determine lifecycle stage
            lifecycle_stage = await self._determine_lifecycle_stage(msme_data, behavior_insights)
            
            # Extract risk and opportunity indicators
            risk_indicators = self._extract_risk_indicators(behavior_insights)
            opportunity_indicators = self._extract_opportunity_indicators(behavior_insights)
            
            # Generate behavioral trends
            behavioral_trends = await self._generate_behavioral_trends(transaction_history)
            
            return BehaviorProfile(
                msme_id=msme_id,
                behavior_score=behavior_score,
                lifecycle_stage=lifecycle_stage,
                behavior_insights=behavior_insights,
                risk_indicators=risk_indicators,
                opportunity_indicators=opportunity_indicators,
                behavioral_trends=behavioral_trends,
                last_updated=datetime.utcnow()
            )
            
        except Exception as e:
            logger.error(f"Error analyzing customer behavior: {str(e)}")
            # Return default profile
            return BehaviorProfile(
                msme_id=msme_data.get('id', 'unknown'),
                behavior_score=50.0,
                lifecycle_stage=CustomerLifecycleStage(
                    stage="unknown",
                    duration_in_stage=0,
                    probability_of_transition={},
                    expected_behaviors=[],
                    risk_factors=["analysis_error"],
                    opportunities=[]
                ),
                behavior_insights=[],
                risk_indicators={},
                opportunity_indicators={},
                behavioral_trends={},
                last_updated=datetime.utcnow()
            )
    
    async def _analyze_payment_behavior(self, transaction_history: List[Dict[str, Any]]) -> BehaviorInsight:
        """Analyze payment regularity and patterns"""
        try:
            # Mock payment analysis (in production, analyze actual payment data)
            payment_delays = [5, 2, 0, 8, 3, 1, 12, 0, 4, 2]  # Days delayed
            avg_delay = np.mean(payment_delays)
            delay_variance = np.var(payment_delays)
            
            # Calculate payment regularity score
            regularity_score = max(0, 100 - (avg_delay * 5) - (delay_variance * 2))
            
            # Determine trend
            recent_delays = payment_delays[-5:]
            earlier_delays = payment_delays[:5]
            trend = "improving" if np.mean(recent_delays) < np.mean(earlier_delays) else "declining"
            
            # Impact on risk
            if regularity_score > 80:
                impact = "positive"
                description = "Excellent payment discipline with minimal delays"
            elif regularity_score > 60:
                impact = "neutral"
                description = "Acceptable payment behavior with occasional delays"
            else:
                impact = "negative"
                description = "Irregular payment patterns indicating potential stress"
            
            # Generate recommendations
            recommendations = []
            if regularity_score < 70:
                recommendations.extend([
                    "Set up automated payment reminders",
                    "Consider payment restructuring options",
                    "Monitor cash flow more closely"
                ])
            else:
                recommendations.extend([
                    "Consider offering payment incentives",
                    "Explore credit limit enhancement"
                ])
            
            return BehaviorInsight(
                pattern_type=BehaviorPattern.PAYMENT_REGULARITY,
                confidence_score=0.85,
                trend_direction=trend,
                impact_on_risk=impact,
                description=description,
                recommendations=recommendations,
                supporting_data={
                    "avg_delay_days": avg_delay,
                    "delay_variance": delay_variance,
                    "regularity_score": regularity_score,
                    "recent_trend": trend
                }
            )
            
        except Exception as e:
            logger.error(f"Error analyzing payment behavior: {str(e)}")
            return BehaviorInsight(
                pattern_type=BehaviorPattern.PAYMENT_REGULARITY,
                confidence_score=0.0,
                trend_direction="unknown",
                impact_on_risk="neutral",
                description="Unable to analyze payment behavior",
                recommendations=["Review payment data quality"],
                supporting_data={}
            )
    
    async def _analyze_cash_flow_patterns(self, transaction_history: List[Dict[str, Any]]) -> BehaviorInsight:
        """Analyze cash flow volatility and patterns"""
        try:
            # Mock cash flow analysis
            monthly_cash_flows = [2500000, 2800000, 2200000, 3100000, 2600000, 
                                2900000, 2400000, 2700000, 2300000, 2800000]
            
            # Calculate volatility metrics
            avg_cash_flow = np.mean(monthly_cash_flows)
            cash_flow_std = np.std(monthly_cash_flows)
            coefficient_of_variation = cash_flow_std / avg_cash_flow
            
            # Determine volatility level
            if coefficient_of_variation < 0.15:
                volatility_level = "low"
                impact = "positive"
                description = "Stable and predictable cash flow patterns"
            elif coefficient_of_variation < 0.30:
                volatility_level = "moderate"
                impact = "neutral"
                description = "Moderate cash flow volatility within acceptable range"
            else:
                volatility_level = "high"
                impact = "negative"
                description = "High cash flow volatility indicating business instability"
            
            # Trend analysis
            recent_flows = monthly_cash_flows[-3:]
            earlier_flows = monthly_cash_flows[:3]
            trend = "improving" if np.mean(recent_flows) > np.mean(earlier_flows) else "declining"
            
            recommendations = []
            if volatility_level == "high":
                recommendations.extend([
                    "Implement cash flow forecasting",
                    "Consider working capital financing",
                    "Review business model sustainability"
                ])
            elif volatility_level == "moderate":
                recommendations.extend([
                    "Monitor seasonal patterns",
                    "Optimize working capital management"
                ])
            
            return BehaviorInsight(
                pattern_type=BehaviorPattern.CASH_FLOW_VOLATILITY,
                confidence_score=0.80,
                trend_direction=trend,
                impact_on_risk=impact,
                description=description,
                recommendations=recommendations,
                supporting_data={
                    "avg_monthly_cash_flow": avg_cash_flow,
                    "volatility_coefficient": coefficient_of_variation,
                    "volatility_level": volatility_level,
                    "trend_direction": trend
                }
            )
            
        except Exception as e:
            logger.error(f"Error analyzing cash flow patterns: {str(e)}")
            return BehaviorInsight(
                pattern_type=BehaviorPattern.CASH_FLOW_VOLATILITY,
                confidence_score=0.0,
                trend_direction="unknown",
                impact_on_risk="neutral",
                description="Unable to analyze cash flow patterns",
                recommendations=["Review transaction data"],
                supporting_data={}
            )
    
    async def _analyze_banking_behavior(self, transaction_history: List[Dict[str, Any]]) -> BehaviorInsight:
        """Analyze banking and financial behavior patterns"""
        try:
            # Mock banking behavior analysis
            digital_payment_ratio = 0.78  # 78% digital payments
            account_utilization = 0.65    # 65% average utilization
            banking_product_usage = 3     # Number of banking products used
            
            # Calculate banking behavior score
            behavior_score = (
                digital_payment_ratio * 40 +
                (1 - account_utilization) * 30 +  # Lower utilization is better
                min(banking_product_usage / 5, 1) * 30
            )
            
            if behavior_score > 75:
                impact = "positive"
                description = "Sophisticated banking behavior with good financial management"
            elif behavior_score > 50:
                impact = "neutral"
                description = "Standard banking behavior with room for improvement"
            else:
                impact = "negative"
                description = "Basic banking behavior indicating limited financial sophistication"
            
            recommendations = []
            if digital_payment_ratio < 0.5:
                recommendations.append("Encourage digital payment adoption")
            if account_utilization > 0.8:
                recommendations.append("Monitor for over-leverage risk")
            if banking_product_usage < 2:
                recommendations.append("Explore additional banking product needs")
            
            return BehaviorInsight(
                pattern_type=BehaviorPattern.BANKING_BEHAVIOR,
                confidence_score=0.75,
                trend_direction="stable",
                impact_on_risk=impact,
                description=description,
                recommendations=recommendations,
                supporting_data={
                    "digital_payment_ratio": digital_payment_ratio,
                    "account_utilization": account_utilization,
                    "banking_products_used": banking_product_usage,
                    "behavior_score": behavior_score
                }
            )
            
        except Exception as e:
            logger.error(f"Error analyzing banking behavior: {str(e)}")
            return BehaviorInsight(
                pattern_type=BehaviorPattern.BANKING_BEHAVIOR,
                confidence_score=0.0,
                trend_direction="unknown",
                impact_on_risk="neutral",
                description="Unable to analyze banking behavior",
                recommendations=["Review banking data"],
                supporting_data={}
            )
    
    async def _analyze_business_seasonality(self, transaction_history: List[Dict[str, Any]]) -> BehaviorInsight:
        """Analyze business seasonality patterns"""
        try:
            # Mock seasonality analysis
            monthly_revenues = [100, 95, 110, 120, 115, 105, 90, 85, 95, 105, 125, 130]  # Index values
            
            # Calculate seasonality metrics
            revenue_range = max(monthly_revenues) - min(monthly_revenues)
            seasonality_index = revenue_range / np.mean(monthly_revenues)
            
            # Identify peak and low seasons
            peak_months = [i for i, v in enumerate(monthly_revenues) if v > np.mean(monthly_revenues) + np.std(monthly_revenues)]
            low_months = [i for i, v in enumerate(monthly_revenues) if v < np.mean(monthly_revenues) - np.std(monthly_revenues)]
            
            if seasonality_index < 0.2:
                seasonality_level = "low"
                impact = "positive"
                description = "Business shows minimal seasonal variation"
            elif seasonality_index < 0.4:
                seasonality_level = "moderate"
                impact = "neutral"
                description = "Business has moderate seasonal patterns"
            else:
                seasonality_level = "high"
                impact = "negative"
                description = "Business highly dependent on seasonal factors"
            
            recommendations = []
            if seasonality_level == "high":
                recommendations.extend([
                    "Plan for seasonal cash flow variations",
                    "Consider seasonal credit facilities",
                    "Diversify revenue streams"
                ])
            
            return BehaviorInsight(
                pattern_type=BehaviorPattern.BUSINESS_SEASONALITY,
                confidence_score=0.70,
                trend_direction="stable",
                impact_on_risk=impact,
                description=description,
                recommendations=recommendations,
                supporting_data={
                    "seasonality_index": seasonality_index,
                    "seasonality_level": seasonality_level,
                    "peak_months": peak_months,
                    "low_months": low_months
                }
            )
            
        except Exception as e:
            logger.error(f"Error analyzing business seasonality: {str(e)}")
            return BehaviorInsight(
                pattern_type=BehaviorPattern.BUSINESS_SEASONALITY,
                confidence_score=0.0,
                trend_direction="unknown",
                impact_on_risk="neutral",
                description="Unable to analyze seasonality patterns",
                recommendations=["Collect more historical data"],
                supporting_data={}
            )
    
    async def _analyze_compliance_behavior(self, msme_data: Dict[str, Any]) -> BehaviorInsight:
        """Analyze compliance and regulatory behavior"""
        try:
            # Mock compliance analysis
            gst_filing_timeliness = 0.92  # 92% on-time filings
            documentation_completeness = 0.88  # 88% complete documentation
            regulatory_responsiveness = 0.85  # 85% timely responses
            
            # Calculate compliance score
            compliance_score = (
                gst_filing_timeliness * 40 +
                documentation_completeness * 35 +
                regulatory_responsiveness * 25
            )
            
            if compliance_score > 85:
                impact = "positive"
                description = "Excellent compliance track record"
            elif compliance_score > 70:
                impact = "neutral"
                description = "Good compliance with minor gaps"
            else:
                impact = "negative"
                description = "Compliance issues requiring attention"
            
            recommendations = []
            if gst_filing_timeliness < 0.9:
                recommendations.append("Improve GST filing processes")
            if documentation_completeness < 0.8:
                recommendations.append("Enhance documentation management")
            if regulatory_responsiveness < 0.8:
                recommendations.append("Improve regulatory communication")
            
            return BehaviorInsight(
                pattern_type=BehaviorPattern.COMPLIANCE_ADHERENCE,
                confidence_score=0.90,
                trend_direction="stable",
                impact_on_risk=impact,
                description=description,
                recommendations=recommendations,
                supporting_data={
                    "gst_filing_timeliness": gst_filing_timeliness,
                    "documentation_completeness": documentation_completeness,
                    "regulatory_responsiveness": regulatory_responsiveness,
                    "compliance_score": compliance_score
                }
            )
            
        except Exception as e:
            logger.error(f"Error analyzing compliance behavior: {str(e)}")
            return BehaviorInsight(
                pattern_type=BehaviorPattern.COMPLIANCE_ADHERENCE,
                confidence_score=0.0,
                trend_direction="unknown",
                impact_on_risk="neutral",
                description="Unable to analyze compliance behavior",
                recommendations=["Review compliance data"],
                supporting_data={}
            )
    
    def _calculate_behavior_score(self, behavior_insights: List[BehaviorInsight]) -> float:
        """Calculate overall behavior score from individual insights"""
        try:
            weighted_score = 0.0
            total_weight = 0.0
            
            for insight in behavior_insights:
                weight = self.behavior_weights.get(insight.pattern_type, 0.1)
                
                # Convert impact to numeric score
                impact_score = {
                    "positive": 80.0,
                    "neutral": 50.0,
                    "negative": 20.0
                }.get(insight.impact_on_risk, 50.0)
                
                weighted_score += impact_score * weight * insight.confidence_score
                total_weight += weight * insight.confidence_score
            
            return weighted_score / max(total_weight, 0.01)
            
        except Exception as e:
            logger.error(f"Error calculating behavior score: {str(e)}")
            return 50.0
    
    async def _determine_lifecycle_stage(self, msme_data: Dict[str, Any], 
                                       behavior_insights: List[BehaviorInsight]) -> CustomerLifecycleStage:
        """Determine customer lifecycle stage"""
        try:
            # Mock lifecycle determination
            account_age_months = msme_data.get('account_age_months', 24)
            business_vintage_years = msme_data.get('business_vintage_years', 3)
            
            # Determine stage based on account age and behavior
            if account_age_months < 12:
                stage = "new_customer"
            elif account_age_months < 36:
                stage = "growing_customer"
            elif account_age_months < 120:
                stage = "mature_customer"
            else:
                # Check for declining indicators
                declining_indicators = sum(1 for insight in behavior_insights 
                                         if insight.impact_on_risk == "negative")
                if declining_indicators > 2:
                    stage = "declining_customer"
                else:
                    stage = "mature_customer"
            
            stage_def = self.lifecycle_definitions[stage]
            
            # Calculate transition probabilities
            transition_probs = {}
            if stage == "new_customer":
                transition_probs = {"growing_customer": 0.7, "declining_customer": 0.2, "mature_customer": 0.1}
            elif stage == "growing_customer":
                transition_probs = {"mature_customer": 0.6, "declining_customer": 0.3, "new_customer": 0.1}
            elif stage == "mature_customer":
                transition_probs = {"declining_customer": 0.4, "growing_customer": 0.3, "mature_customer": 0.3}
            else:  # declining_customer
                transition_probs = {"mature_customer": 0.4, "growing_customer": 0.3, "declining_customer": 0.3}
            
            return CustomerLifecycleStage(
                stage=stage,
                duration_in_stage=account_age_months,
                probability_of_transition=transition_probs,
                expected_behaviors=stage_def["typical_behaviors"],
                risk_factors=stage_def["risk_factors"],
                opportunities=stage_def["opportunities"]
            )
            
        except Exception as e:
            logger.error(f"Error determining lifecycle stage: {str(e)}")
            return CustomerLifecycleStage(
                stage="unknown",
                duration_in_stage=0,
                probability_of_transition={},
                expected_behaviors=[],
                risk_factors=[],
                opportunities=[]
            )
    
    def _extract_risk_indicators(self, behavior_insights: List[BehaviorInsight]) -> Dict[str, float]:
        """Extract risk indicators from behavior insights"""
        risk_indicators = {}
        
        for insight in behavior_insights:
            if insight.impact_on_risk == "negative":
                risk_indicators[insight.pattern_type.value] = 1.0 - insight.confidence_score
            elif insight.impact_on_risk == "neutral":
                risk_indicators[insight.pattern_type.value] = 0.5
            else:
                risk_indicators[insight.pattern_type.value] = 0.2
        
        return risk_indicators
    
    def _extract_opportunity_indicators(self, behavior_insights: List[BehaviorInsight]) -> Dict[str, float]:
        """Extract opportunity indicators from behavior insights"""
        opportunity_indicators = {}
        
        for insight in behavior_insights:
            if insight.impact_on_risk == "positive":
                opportunity_indicators[insight.pattern_type.value] = insight.confidence_score
            elif insight.impact_on_risk == "neutral":
                opportunity_indicators[insight.pattern_type.value] = 0.5
            else:
                opportunity_indicators[insight.pattern_type.value] = 0.2
        
        return opportunity_indicators
    
    async def _generate_behavioral_trends(self, transaction_history: List[Dict[str, Any]]) -> Dict[str, List[float]]:
        """Generate behavioral trend data"""
        try:
            # Mock trend data (in production, calculate from actual history)
            trends = {
                "payment_regularity": [75, 78, 82, 80, 85, 88, 90, 87, 89, 92],
                "cash_flow_stability": [60, 65, 70, 68, 72, 75, 78, 80, 82, 85],
                "digital_adoption": [45, 50, 55, 60, 65, 68, 72, 75, 78, 80],
                "compliance_score": [80, 82, 85, 88, 90, 88, 92, 94, 96, 95]
            }
            
            return trends
            
        except Exception as e:
            logger.error(f"Error generating behavioral trends: {str(e)}")
            return {}

# Global instance for use across the application
behavioral_analytics = BehavioralAnalyticsEngine()
