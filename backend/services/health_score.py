"""
Health Score Engine v0.1
Implements the specific scoring logic for MSME credit assessment
"""
from typing import List, Dict, Any
from datetime import datetime, <PERSON><PERSON><PERSON>

def calculate_health_score(signals: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Calculate health score based on the v0.1 algorithm:
    - Base score: 100
    - GST drop > 20%: -20 points
    - Google review drop > 30%: -15 points
    - UPI merchant count < 3: -10 points
    """
    
    # Base score
    score = 100
    breakdown = {
        'base_score': 100,
        'gst_penalty': 0,
        'reviews_penalty': 0,
        'upi_penalty': 0,
        'details': {}
    }
    
    if not signals:
        return {
            'score': 40,  # Default low score for no data
            'risk_band': 'red',
            'breakdown': breakdown
        }
    
    # Analyze GST signals
    gst_analysis = _analyze_gst_signals(signals)
    if gst_analysis['drop_percentage'] > 20:
        penalty = 20
        score -= penalty
        breakdown['gst_penalty'] = penalty
        breakdown['details']['gst'] = f"GST drop of {gst_analysis['drop_percentage']:.1f}% detected"
    
    # Analyze Google Reviews signals
    reviews_analysis = _analyze_reviews_signals(signals)
    if reviews_analysis['drop_percentage'] > 30:
        penalty = 15
        score -= penalty
        breakdown['reviews_penalty'] = penalty
        breakdown['details']['reviews'] = f"Reviews drop of {reviews_analysis['drop_percentage']:.1f}% detected"
    
    # Analyze UPI signals
    upi_analysis = _analyze_upi_signals(signals)
    if upi_analysis['merchant_count'] < 3:
        penalty = 10
        score -= penalty
        breakdown['upi_penalty'] = penalty
        breakdown['details']['upi'] = f"Low UPI diversity: {upi_analysis['merchant_count']} merchants"
    
    # Ensure score doesn't go below 0
    score = max(0, score)
    
    # Determine risk band
    risk_band = _determine_risk_band(score)
    
    return {
        'score': score,
        'risk_band': risk_band,
        'breakdown': breakdown
    }

def _analyze_gst_signals(signals: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Analyze GST turnover signals for drops"""
    gst_signals = [s for s in signals if s.get('source') == 'gst']
    
    if len(gst_signals) < 2:
        return {'drop_percentage': 0, 'current_value': 0, 'previous_value': 0}
    
    # Sort by timestamp (most recent first)
    gst_signals.sort(key=lambda x: x.get('timestamp', ''), reverse=True)
    
    current_value = gst_signals[0].get('value', 0)
    previous_value = gst_signals[1].get('value', 0)
    
    if previous_value == 0:
        return {'drop_percentage': 0, 'current_value': current_value, 'previous_value': previous_value}
    
    drop_percentage = ((previous_value - current_value) / previous_value) * 100
    drop_percentage = max(0, drop_percentage)  # Only consider drops, not increases
    
    return {
        'drop_percentage': drop_percentage,
        'current_value': current_value,
        'previous_value': previous_value
    }

def _analyze_reviews_signals(signals: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Analyze Google Reviews signals for drops"""
    review_signals = [s for s in signals if s.get('source') == 'reviews']
    
    if len(review_signals) < 2:
        return {'drop_percentage': 0, 'current_count': 0, 'previous_count': 0}
    
    # Sort by timestamp (most recent first)
    review_signals.sort(key=lambda x: x.get('timestamp', ''), reverse=True)
    
    current_reviews = review_signals[0].get('value', {})
    previous_reviews = review_signals[1].get('value', {})
    
    current_count = current_reviews.get('review_count', 0) if isinstance(current_reviews, dict) else 0
    previous_count = previous_reviews.get('review_count', 0) if isinstance(previous_reviews, dict) else 0
    
    if previous_count == 0:
        return {'drop_percentage': 0, 'current_count': current_count, 'previous_count': previous_count}
    
    drop_percentage = ((previous_count - current_count) / previous_count) * 100
    drop_percentage = max(0, drop_percentage)  # Only consider drops
    
    return {
        'drop_percentage': drop_percentage,
        'current_count': current_count,
        'previous_count': previous_count
    }

def _analyze_upi_signals(signals: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Analyze UPI signals for merchant diversity"""
    upi_signals = [s for s in signals if s.get('source') == 'upi']
    
    if not upi_signals:
        return {'merchant_count': 0, 'weekly_merchants': []}
    
    # Get the most recent UPI signal
    upi_signals.sort(key=lambda x: x.get('timestamp', ''), reverse=True)
    latest_upi = upi_signals[0]
    
    upi_value = latest_upi.get('value', {})
    
    # Extract merchant count from UPI data
    if isinstance(upi_value, dict):
        # If merchant count is directly provided
        merchant_count = upi_value.get('merchant_count', 0)
        
        # If not provided, estimate from transaction diversity
        if merchant_count == 0:
            transaction_count = upi_value.get('transaction_count', 0)
            # Rough estimate: assume 1 merchant per 10 transactions minimum
            merchant_count = min(transaction_count // 10, 10)  # Cap at 10 for estimation
    else:
        merchant_count = 0
    
    return {
        'merchant_count': merchant_count,
        'latest_upi_data': upi_value
    }

def _determine_risk_band(score: int) -> str:
    """Determine risk band based on score"""
    if score >= 70:
        return 'green'
    elif score >= 40:
        return 'yellow'
    else:
        return 'red'

def simulate_msme_data() -> List[Dict[str, Any]]:
    """Generate simulated MSME data for testing"""
    return [
        {
            'msme_id': 'arjun_kirana_001',
            'name': 'Arjun Kirana Store',
            'location': 'Hyderabad',
            'business_type': 'retail',
            'signals': [
                {
                    'source': 'gst',
                    'value': 80000,  # Dropped from 120000
                    'timestamp': datetime.utcnow().isoformat(),
                    'metadata': {'previous_value': 120000}
                },
                {
                    'source': 'upi',
                    'value': {'transaction_count': 15, 'merchant_count': 2},
                    'timestamp': datetime.utcnow().isoformat(),
                    'metadata': {'weekly_data': True}
                }
            ]
        },
        {
            'msme_id': 'kiran_tailors_002',
            'name': 'Kiran Tailors',
            'location': 'Bhopal',
            'business_type': 'services',
            'signals': [
                {
                    'source': 'reviews',
                    'value': {'review_count': 90, 'average_rating': 3.8},
                    'timestamp': datetime.utcnow().isoformat(),
                    'metadata': {'previous_count': 150}
                },
                {
                    'source': 'gst',
                    'value': 95000,  # Stable
                    'timestamp': datetime.utcnow().isoformat(),
                    'metadata': {'previous_value': 98000}
                }
            ]
        },
        {
            'msme_id': 'rana_cement_003',
            'name': 'Rana Cement Distributors',
            'location': 'Pune',
            'business_type': 'b2b',
            'signals': [
                {
                    'source': 'gst',
                    'value': 450000,  # Stable
                    'timestamp': datetime.utcnow().isoformat(),
                    'metadata': {'previous_value': 440000}
                },
                {
                    'source': 'upi',
                    'value': {'transaction_count': 85, 'merchant_count': 8},
                    'timestamp': datetime.utcnow().isoformat(),
                    'metadata': {'weekly_data': True}
                },
                {
                    'source': 'reviews',
                    'value': {'review_count': 45, 'average_rating': 4.2},
                    'timestamp': datetime.utcnow().isoformat(),
                    'metadata': {'previous_count': 42}
                }
            ]
        }
    ]
