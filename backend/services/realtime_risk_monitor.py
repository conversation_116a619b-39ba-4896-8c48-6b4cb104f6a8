"""
Real-time Risk Monitoring Dashboard
Executive-level portfolio monitoring with live updates
"""

import asyncio
import json
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
import logging
from concurrent.futures import ThreadPoolExecutor
import numpy as np

logger = logging.getLogger(__name__)

class RiskEventType(Enum):
    SCORE_CHANGE = "score_change"
    PAYMENT_DELAY = "payment_delay"
    LIMIT_BREACH = "limit_breach"
    COMPLIANCE_ALERT = "compliance_alert"
    CONCENTRATION_RISK = "concentration_risk"
    EARLY_WARNING = "early_warning"

@dataclass
class RiskEvent:
    event_id: str
    msme_id: str
    event_type: RiskEventType
    severity: str
    title: str
    description: str
    impact_score: float
    timestamp: datetime
    metadata: Dict[str, Any]

@dataclass
class PortfolioMetrics:
    total_msmes: int
    total_exposure: float
    avg_risk_score: float
    risk_distribution: Dict[str, int]
    npa_ratio: float
    sma_ratio: float
    portfolio_health_score: float
    concentration_metrics: Dict[str, float]
    trend_indicators: Dict[str, float]
    last_updated: datetime

@dataclass
class RiskHeatmapData:
    geographic_risk: Dict[str, float]
    sector_risk: Dict[str, float]
    exposure_concentration: Dict[str, float]
    time_series_risk: List[Dict[str, Any]]

class RealTimeRiskMonitor:
    """
    Real-time risk monitoring system for executive dashboard
    Provides live portfolio metrics and risk event streaming
    """
    
    def __init__(self):
        self.risk_events = []
        self.portfolio_cache = {}
        self.subscribers = {}
        self.monitoring_active = True
        self.executor = ThreadPoolExecutor(max_workers=6)
        self.risk_thresholds = self._initialize_risk_thresholds()
        
    def _initialize_risk_thresholds(self) -> Dict[str, float]:
        """Initialize risk monitoring thresholds"""
        return {
            "portfolio_npa_threshold": 0.08,  # 8% NPA threshold
            "concentration_threshold": 0.15,  # 15% single borrower limit
            "sector_concentration_threshold": 0.25,  # 25% sector limit
            "geographic_concentration_threshold": 0.30,  # 30% geographic limit
            "score_drop_threshold": 15.0,  # 15 point score drop alert
            "payment_delay_threshold": 30,  # 30 days payment delay
            "exposure_growth_threshold": 0.20  # 20% exposure growth alert
        }
    
    async def get_realtime_portfolio_metrics(self) -> PortfolioMetrics:
        """
        Get real-time portfolio metrics for executive dashboard
        """
        try:
            # Simulate real-time data processing
            current_time = datetime.utcnow()
            
            # Calculate portfolio metrics
            total_msmes = 1247
            total_exposure = 124700000000  # 1247 Cr
            
            # Risk distribution (realistic distribution)
            risk_distribution = {
                "green": 623,   # 50% low risk
                "yellow": 436,  # 35% medium risk  
                "red": 188      # 15% high risk
            }
            
            # Calculate average risk score
            avg_risk_score = (
                (risk_distribution["green"] * 80) +
                (risk_distribution["yellow"] * 55) +
                (risk_distribution["red"] * 25)
            ) / total_msmes
            
            # Calculate ratios
            npa_ratio = 0.067  # 6.7% NPA ratio
            sma_ratio = 0.089  # 8.9% SMA ratio
            
            # Portfolio health score (0-100)
            portfolio_health_score = self._calculate_portfolio_health(
                avg_risk_score, npa_ratio, sma_ratio, risk_distribution, total_msmes
            )
            
            # Concentration metrics
            concentration_metrics = await self._calculate_concentration_metrics()
            
            # Trend indicators
            trend_indicators = await self._calculate_trend_indicators()
            
            return PortfolioMetrics(
                total_msmes=total_msmes,
                total_exposure=total_exposure,
                avg_risk_score=avg_risk_score,
                risk_distribution=risk_distribution,
                npa_ratio=npa_ratio,
                sma_ratio=sma_ratio,
                portfolio_health_score=portfolio_health_score,
                concentration_metrics=concentration_metrics,
                trend_indicators=trend_indicators,
                last_updated=current_time
            )
            
        except Exception as e:
            logger.error(f"Error calculating portfolio metrics: {str(e)}")
            # Return safe defaults
            return PortfolioMetrics(
                total_msmes=0,
                total_exposure=0.0,
                avg_risk_score=50.0,
                risk_distribution={"green": 0, "yellow": 0, "red": 0},
                npa_ratio=0.0,
                sma_ratio=0.0,
                portfolio_health_score=50.0,
                concentration_metrics={},
                trend_indicators={},
                last_updated=datetime.utcnow()
            )
    
    def _calculate_portfolio_health(self, avg_risk_score: float, npa_ratio: float, 
                                  sma_ratio: float, risk_distribution: Dict[str, int], 
                                  total_msmes: int) -> float:
        """Calculate overall portfolio health score"""
        
        # Base score from average risk score
        base_score = avg_risk_score
        
        # Penalties for high NPA/SMA ratios
        npa_penalty = min(30, npa_ratio * 300)  # Max 30 point penalty
        sma_penalty = min(20, sma_ratio * 200)  # Max 20 point penalty
        
        # Penalty for high risk concentration
        high_risk_pct = risk_distribution["red"] / total_msmes
        concentration_penalty = min(25, high_risk_pct * 100)  # Max 25 point penalty
        
        # Calculate final health score
        health_score = base_score - npa_penalty - sma_penalty - concentration_penalty
        
        return max(0, min(100, health_score))
    
    async def _calculate_concentration_metrics(self) -> Dict[str, float]:
        """Calculate portfolio concentration metrics"""
        return {
            "single_borrower_max": 0.12,  # 12% max single borrower exposure
            "top_10_borrowers": 0.45,     # 45% exposure in top 10
            "manufacturing_sector": 0.28,  # 28% in manufacturing
            "mumbai_geographic": 0.22,     # 22% in Mumbai
            "high_risk_concentration": 0.15,  # 15% in high risk
            "large_exposure_count": 23     # 23 large exposures (>5Cr)
        }
    
    async def _calculate_trend_indicators(self) -> Dict[str, float]:
        """Calculate portfolio trend indicators"""
        return {
            "score_trend_30d": -2.3,      # -2.3 point average decline
            "npa_trend_30d": 0.008,       # +0.8% NPA increase
            "exposure_growth_30d": 0.034,  # +3.4% exposure growth
            "new_accounts_30d": 47,        # 47 new accounts
            "closed_accounts_30d": 12,     # 12 closed accounts
            "upgrade_rate_30d": 0.067,     # 6.7% upgrade rate
            "downgrade_rate_30d": 0.089    # 8.9% downgrade rate
        }
    
    async def get_risk_heatmap_data(self) -> RiskHeatmapData:
        """
        Generate risk heatmap data for visualization
        """
        try:
            # Geographic risk distribution
            geographic_risk = {
                "mumbai": 0.18,
                "delhi": 0.22,
                "bangalore": 0.15,
                "chennai": 0.19,
                "pune": 0.21,
                "hyderabad": 0.24,
                "ahmedabad": 0.26,
                "kolkata": 0.28,
                "jaipur": 0.31,
                "lucknow": 0.29
            }
            
            # Sector risk distribution
            sector_risk = {
                "manufacturing": 0.23,
                "retail": 0.28,
                "services": 0.19,
                "agriculture": 0.35,
                "textile": 0.31,
                "food_beverage": 0.25,
                "construction": 0.38,
                "healthcare": 0.16,
                "education": 0.14,
                "transport": 0.27
            }
            
            # Exposure concentration by size
            exposure_concentration = {
                "micro": 0.25,      # <10L
                "small": 0.45,      # 10L-1Cr
                "medium": 0.30      # >1Cr
            }
            
            # Time series risk data (last 30 days)
            time_series_risk = []
            base_date = datetime.utcnow() - timedelta(days=30)
            
            for i in range(30):
                date = base_date + timedelta(days=i)
                # Simulate realistic risk trend
                base_risk = 0.15 + 0.02 * np.sin(i * 0.2) + np.random.normal(0, 0.005)
                time_series_risk.append({
                    "date": date.isoformat(),
                    "portfolio_risk": max(0.05, min(0.35, base_risk)),
                    "npa_ratio": max(0.02, min(0.12, 0.067 + np.random.normal(0, 0.003))),
                    "avg_score": max(40, min(85, 62 + np.random.normal(0, 2)))
                })
            
            return RiskHeatmapData(
                geographic_risk=geographic_risk,
                sector_risk=sector_risk,
                exposure_concentration=exposure_concentration,
                time_series_risk=time_series_risk
            )
            
        except Exception as e:
            logger.error(f"Error generating heatmap data: {str(e)}")
            return RiskHeatmapData(
                geographic_risk={},
                sector_risk={},
                exposure_concentration={},
                time_series_risk=[]
            )
    
    async def generate_risk_events(self) -> List[RiskEvent]:
        """
        Generate real-time risk events for monitoring
        """
        try:
            events = []
            current_time = datetime.utcnow()
            
            # Score change events
            events.append(RiskEvent(
                event_id=f"score_change_{current_time.timestamp()}",
                msme_id="msme_001",
                event_type=RiskEventType.SCORE_CHANGE,
                severity="high",
                title="Significant Score Drop Detected",
                description="ABC Manufacturing score dropped 18 points in 7 days",
                impact_score=85.0,
                timestamp=current_time - timedelta(minutes=15),
                metadata={
                    "previous_score": 72,
                    "current_score": 54,
                    "change": -18,
                    "msme_name": "ABC Manufacturing",
                    "exposure": 15000000
                }
            ))
            
            # Payment delay events
            events.append(RiskEvent(
                event_id=f"payment_delay_{current_time.timestamp()}",
                msme_id="msme_002",
                event_type=RiskEventType.PAYMENT_DELAY,
                severity="medium",
                title="Payment Delay Alert",
                description="XYZ Retail payment overdue by 45 days",
                impact_score=70.0,
                timestamp=current_time - timedelta(hours=2),
                metadata={
                    "days_overdue": 45,
                    "amount_overdue": 2500000,
                    "msme_name": "XYZ Retail",
                    "last_payment": (current_time - timedelta(days=45)).isoformat()
                }
            ))
            
            # Concentration risk events
            events.append(RiskEvent(
                event_id=f"concentration_{current_time.timestamp()}",
                msme_id="portfolio_wide",
                event_type=RiskEventType.CONCENTRATION_RISK,
                severity="medium",
                title="Sector Concentration Alert",
                description="Manufacturing sector exposure exceeds 25% threshold",
                impact_score=65.0,
                timestamp=current_time - timedelta(hours=6),
                metadata={
                    "sector": "manufacturing",
                    "current_exposure_pct": 28.5,
                    "threshold": 25.0,
                    "total_exposure": 35500000000
                }
            ))
            
            # Early warning events
            events.append(RiskEvent(
                event_id=f"early_warning_{current_time.timestamp()}",
                msme_id="msme_003",
                event_type=RiskEventType.EARLY_WARNING,
                severity="high",
                title="Early Warning Signal",
                description="Gujarat Chemicals showing multiple stress indicators",
                impact_score=90.0,
                timestamp=current_time - timedelta(minutes=30),
                metadata={
                    "stress_indicators": [
                        "GST filing delays",
                        "Banking irregularities", 
                        "Declining cash flows"
                    ],
                    "msme_name": "Gujarat Chemicals",
                    "exposure": ********,
                    "probability_of_default": 0.32
                }
            ))
            
            return sorted(events, key=lambda x: x.impact_score, reverse=True)
            
        except Exception as e:
            logger.error(f"Error generating risk events: {str(e)}")
            return []
    
    async def get_executive_summary(self) -> Dict[str, Any]:
        """
        Generate executive summary for dashboard
        """
        try:
            portfolio_metrics = await self.get_realtime_portfolio_metrics()
            risk_events = await self.generate_risk_events()
            
            # Calculate key insights
            high_priority_events = [e for e in risk_events if e.severity == "high"]
            total_at_risk_exposure = sum(
                e.metadata.get("exposure", 0) for e in high_priority_events
            )
            
            # Generate executive insights
            insights = []
            
            if portfolio_metrics.portfolio_health_score < 70:
                insights.append("Portfolio health below target - immediate review recommended")
            
            if portfolio_metrics.npa_ratio > self.risk_thresholds["portfolio_npa_threshold"]:
                insights.append(f"NPA ratio ({portfolio_metrics.npa_ratio:.1%}) exceeds threshold")
            
            if len(high_priority_events) > 0:
                insights.append(f"{len(high_priority_events)} high-priority alerts require attention")
            
            if total_at_risk_exposure > 50000000:  # 5Cr threshold
                insights.append(f"₹{total_at_risk_exposure/10000000:.1f}Cr exposure at high risk")
            
            # Generate recommendations
            recommendations = []
            
            if portfolio_metrics.portfolio_health_score < 60:
                recommendations.append("Convene emergency risk committee meeting")
            elif portfolio_metrics.portfolio_health_score < 75:
                recommendations.append("Increase portfolio monitoring frequency")
            
            if len(high_priority_events) > 3:
                recommendations.append("Deploy additional field officers for verification")
            
            if portfolio_metrics.concentration_metrics.get("manufacturing_sector", 0) > 0.25:
                recommendations.append("Reduce manufacturing sector concentration")
            
            return {
                "portfolio_health_score": portfolio_metrics.portfolio_health_score,
                "total_exposure": portfolio_metrics.total_exposure,
                "high_risk_count": portfolio_metrics.risk_distribution["red"],
                "npa_ratio": portfolio_metrics.npa_ratio,
                "key_insights": insights,
                "recommendations": recommendations,
                "critical_events_count": len(high_priority_events),
                "at_risk_exposure": total_at_risk_exposure,
                "last_updated": datetime.utcnow().isoformat(),
                "trend_direction": "declining" if portfolio_metrics.trend_indicators.get("score_trend_30d", 0) < 0 else "stable"
            }
            
        except Exception as e:
            logger.error(f"Error generating executive summary: {str(e)}")
            return {
                "portfolio_health_score": 50.0,
                "total_exposure": 0.0,
                "high_risk_count": 0,
                "npa_ratio": 0.0,
                "key_insights": ["Error generating insights"],
                "recommendations": ["Contact system administrator"],
                "critical_events_count": 0,
                "at_risk_exposure": 0.0,
                "last_updated": datetime.utcnow().isoformat(),
                "trend_direction": "unknown"
            }
    
    async def start_monitoring(self):
        """Start real-time monitoring background tasks"""
        try:
            self.monitoring_active = True
            logger.info("Real-time risk monitoring started")
            
            # In production, this would start background tasks for:
            # - Real-time data ingestion
            # - Event stream processing  
            # - Alert generation
            # - WebSocket broadcasting
            
        except Exception as e:
            logger.error(f"Error starting monitoring: {str(e)}")
    
    async def stop_monitoring(self):
        """Stop real-time monitoring"""
        self.monitoring_active = False
        logger.info("Real-time risk monitoring stopped")

# Global instance for use across the application
realtime_monitor = RealTimeRiskMonitor()
