import asyncio
import aiohttp
import logging
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta

from models.gst_data import (
    GSTTurnoverData, GSTComplianceRecord, GSTPaymentPattern, 
    GSTAnalytics, GSTDataRequest, GSTDataResponse, GSTHealthCheck,
    GSTReturnType, GSTReturnStatus, GSTComplianceStatus
)
from firebase.init import get_firestore_client

logger = logging.getLogger(__name__)

class GSTService:
    """Service for GST data integration and analysis"""
    
    def __init__(self):
        self.api_base_url = "https://api.gstn.gov.in"  # Mock URL - replace with actual GSTN API
        self.client_id = "your_client_id"  # From environment variables
        self.client_secret = "your_client_secret"  # From environment variables
        self.db = get_firestore_client()
        
    async def authenticate_gstn_api(self) -> str:
        """Authenticate with GSTN API and get access token"""
        try:
            auth_url = f"{self.api_base_url}/auth/token"
            auth_data = {
                "client_id": self.client_id,
                "client_secret": self.client_secret,
                "grant_type": "client_credentials"
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(auth_url, data=auth_data) as response:
                    if response.status == 200:
                        auth_response = await response.json()
                        return auth_response.get("access_token")
                    else:
                        logger.error(f"GST API authentication failed: {response.status}")
                        return None
                        
        except Exception as e:
            logger.error(f"GST API authentication error: {str(e)}")
            return None
    
    async def fetch_gst_turnover_data(self, gstin: str, period: str, return_type: GSTReturnType) -> Optional[GSTTurnoverData]:
        """Fetch GST turnover data from GSTN API"""
        try:
            access_token = await self.authenticate_gstn_api()
            if not access_token:
                return None
                
            # Mock implementation - replace with actual GSTN API calls
            # In production, this would call the actual GSTN API endpoints
            mock_data = self._generate_mock_gst_turnover_data(gstin, period, return_type)
            
            # Store in Firestore
            await self._store_gst_turnover_data(mock_data)
            
            return mock_data
            
        except Exception as e:
            logger.error(f"Error fetching GST turnover data: {str(e)}")
            return None
    
    async def fetch_gst_compliance_data(self, gstin: str, from_period: str, to_period: str) -> List[GSTComplianceRecord]:
        """Fetch GST compliance data for a period range"""
        try:
            compliance_records = []
            
            # Generate period list
            periods = self._generate_period_list(from_period, to_period)
            
            for period in periods:
                # Mock compliance data - replace with actual API calls
                compliance_record = self._generate_mock_compliance_record(gstin, period)
                compliance_records.append(compliance_record)
                
                # Store in Firestore
                await self._store_gst_compliance_record(compliance_record)
            
            return compliance_records
            
        except Exception as e:
            logger.error(f"Error fetching GST compliance data: {str(e)}")
            return []
    
    async def analyze_gst_payment_patterns(self, gstin: str, periods: List[str]) -> List[GSTPaymentPattern]:
        """Analyze GST payment patterns"""
        try:
            payment_patterns = []
            
            for period in periods:
                # Mock payment pattern analysis
                pattern = self._generate_mock_payment_pattern(gstin, period)
                payment_patterns.append(pattern)
                
                # Store in Firestore
                await self._store_gst_payment_pattern(pattern)
            
            return payment_patterns
            
        except Exception as e:
            logger.error(f"Error analyzing GST payment patterns: {str(e)}")
            return []
    
    async def calculate_gst_analytics(self, gstin: str, msme_id: str) -> Optional[GSTAnalytics]:
        """Calculate comprehensive GST analytics"""
        try:
            # Fetch historical data
            turnover_data = await self._get_stored_turnover_data(gstin)
            compliance_records = await self._get_stored_compliance_records(gstin)
            payment_patterns = await self._get_stored_payment_patterns(gstin)
            
            if not turnover_data:
                return None
            
            # Calculate analytics
            analytics = GSTAnalytics(
                gstin=gstin,
                msme_id=msme_id,
                avg_monthly_turnover=self._calculate_avg_monthly_turnover(turnover_data),
                turnover_growth_rate=self._calculate_turnover_growth_rate(turnover_data),
                turnover_volatility=self._calculate_turnover_volatility(turnover_data),
                seasonal_index=self._calculate_seasonal_index(turnover_data),
                filing_compliance_rate=self._calculate_filing_compliance_rate(compliance_records),
                payment_compliance_rate=self._calculate_payment_compliance_rate(payment_patterns),
                avg_filing_delay=self._calculate_avg_filing_delay(compliance_records),
                avg_payment_delay=self._calculate_avg_payment_delay(payment_patterns),
                itc_utilization_rate=self._calculate_itc_utilization_rate(turnover_data),
                itc_efficiency=self._calculate_itc_efficiency(turnover_data),
                avg_itc_balance=self._calculate_avg_itc_balance(turnover_data),
                compliance_risk_score=self._calculate_compliance_risk_score(compliance_records),
                payment_risk_score=self._calculate_payment_risk_score(payment_patterns),
                overall_gst_score=0.0,  # Will be calculated below
                turnover_trend=self._determine_turnover_trend(turnover_data),
                compliance_trend=self._determine_compliance_trend(compliance_records),
                data_quality_score=self._calculate_data_quality_score(turnover_data, compliance_records)
            )
            
            # Calculate overall GST score
            analytics.overall_gst_score = self._calculate_overall_gst_score(analytics)
            
            # Store analytics
            await self._store_gst_analytics(analytics)
            
            return analytics
            
        except Exception as e:
            logger.error(f"Error calculating GST analytics: {str(e)}")
            return None
    
    async def get_gst_health_check(self, gstin: str) -> GSTHealthCheck:
        """Get GST data source health check"""
        try:
            # Check last sync status
            last_sync_doc = self.db.collection('gst_sync_status').document(gstin).get()
            
            if last_sync_doc.exists:
                sync_data = last_sync_doc.to_dict()
                return GSTHealthCheck(
                    gstin=gstin,
                    last_sync=sync_data.get('last_sync', datetime.utcnow()),
                    sync_status=sync_data.get('sync_status', 'unknown'),
                    data_completeness=sync_data.get('data_completeness', 0.0),
                    api_response_time=sync_data.get('api_response_time', 0.0),
                    error_count=sync_data.get('error_count', 0),
                    next_sync_scheduled=sync_data.get('next_sync_scheduled', datetime.utcnow() + timedelta(hours=24))
                )
            else:
                return GSTHealthCheck(
                    gstin=gstin,
                    last_sync=datetime.utcnow(),
                    sync_status='never_synced',
                    data_completeness=0.0,
                    api_response_time=0.0,
                    error_count=0,
                    next_sync_scheduled=datetime.utcnow() + timedelta(hours=1)
                )
                
        except Exception as e:
            logger.error(f"Error getting GST health check: {str(e)}")
            return GSTHealthCheck(
                gstin=gstin,
                last_sync=datetime.utcnow(),
                sync_status='error',
                data_completeness=0.0,
                api_response_time=0.0,
                error_count=1,
                next_sync_scheduled=datetime.utcnow() + timedelta(hours=1)
            )
    
    # Helper methods for calculations
    def _calculate_avg_monthly_turnover(self, turnover_data: List[GSTTurnoverData]) -> float:
        """Calculate average monthly turnover"""
        if not turnover_data:
            return 0.0
        
        total_turnover = sum(data.total_turnover for data in turnover_data)
        return total_turnover / len(turnover_data)
    
    def _calculate_turnover_growth_rate(self, turnover_data: List[GSTTurnoverData]) -> float:
        """Calculate turnover growth rate"""
        if len(turnover_data) < 2:
            return 0.0
        
        # Sort by period
        sorted_data = sorted(turnover_data, key=lambda x: x.period)
        
        first_period = sorted_data[0].total_turnover
        last_period = sorted_data[-1].total_turnover
        
        if first_period == 0:
            return 0.0
        
        periods_count = len(sorted_data) - 1
        growth_rate = ((last_period / first_period) ** (1 / periods_count) - 1) * 100
        
        return round(growth_rate, 2)
    
    def _calculate_turnover_volatility(self, turnover_data: List[GSTTurnoverData]) -> float:
        """Calculate turnover volatility coefficient"""
        if len(turnover_data) < 2:
            return 0.0
        
        turnovers = [data.total_turnover for data in turnover_data]
        mean_turnover = sum(turnovers) / len(turnovers)
        
        if mean_turnover == 0:
            return 0.0
        
        variance = sum((x - mean_turnover) ** 2 for x in turnovers) / len(turnovers)
        std_deviation = variance ** 0.5
        
        coefficient_of_variation = (std_deviation / mean_turnover) * 100
        return round(coefficient_of_variation, 2)
    
    def _calculate_filing_compliance_rate(self, compliance_records: List[GSTComplianceRecord]) -> float:
        """Calculate filing compliance rate"""
        if not compliance_records:
            return 0.0
        
        filed_on_time = sum(1 for record in compliance_records 
                           if record.status == GSTReturnStatus.FILED and 
                           (record.days_delayed is None or record.days_delayed <= 0))
        
        return (filed_on_time / len(compliance_records)) * 100
    
    def _calculate_overall_gst_score(self, analytics: GSTAnalytics) -> float:
        """Calculate overall GST health score"""
        # Weighted scoring
        weights = {
            'filing_compliance': 0.25,
            'payment_compliance': 0.25,
            'turnover_stability': 0.20,
            'itc_efficiency': 0.15,
            'growth_trend': 0.15
        }
        
        # Normalize scores to 0-100 scale
        filing_score = analytics.filing_compliance_rate
        payment_score = analytics.payment_compliance_rate
        
        # Turnover stability (inverse of volatility)
        turnover_stability = max(0, 100 - analytics.turnover_volatility)
        
        itc_score = analytics.itc_efficiency
        
        # Growth trend score
        growth_score = min(100, max(0, 50 + analytics.turnover_growth_rate))
        
        overall_score = (
            filing_score * weights['filing_compliance'] +
            payment_score * weights['payment_compliance'] +
            turnover_stability * weights['turnover_stability'] +
            itc_score * weights['itc_efficiency'] +
            growth_score * weights['growth_trend']
        )
        
        return round(overall_score, 2)
    
    # Mock data generation methods (replace with actual API calls in production)
    def _generate_mock_gst_turnover_data(self, gstin: str, period: str, return_type: GSTReturnType) -> GSTTurnoverData:
        """Generate mock GST turnover data for development"""
        import random
        
        base_turnover = random.uniform(500000, 5000000)  # 5L to 50L
        
        return GSTTurnoverData(
            gstin=gstin,
            period=period,
            return_type=return_type,
            total_turnover=base_turnover,
            taxable_turnover=base_turnover * 0.9,
            exempt_turnover=base_turnover * 0.05,
            export_turnover=base_turnover * 0.05,
            total_tax_liability=base_turnover * 0.18 * 0.9,  # 18% GST on taxable turnover
            cgst_liability=base_turnover * 0.09 * 0.9,
            sgst_liability=base_turnover * 0.09 * 0.9,
            igst_liability=0.0,
            cess_liability=0.0,
            total_itc_claimed=base_turnover * 0.12,
            itc_utilized=base_turnover * 0.10,
            itc_carried_forward=base_turnover * 0.02,
            cash_payment=base_turnover * 0.08,
            itc_payment=base_turnover * 0.10,
            filing_date=datetime.utcnow() - timedelta(days=random.randint(1, 30)),
            due_date=datetime.utcnow() - timedelta(days=random.randint(31, 45))
        )

    def _generate_mock_compliance_record(self, gstin: str, period: str) -> GSTComplianceRecord:
        """Generate mock compliance record"""
        import random

        due_date = datetime.utcnow() - timedelta(days=random.randint(30, 60))
        filing_date = due_date + timedelta(days=random.randint(-5, 15))
        days_delayed = max(0, (filing_date - due_date).days)

        status = GSTReturnStatus.FILED if random.random() > 0.1 else GSTReturnStatus.NOT_FILED
        if status == GSTReturnStatus.FILED and days_delayed > 0:
            status = GSTReturnStatus.LATE_FILED

        compliance_status = GSTComplianceStatus.COMPLIANT
        if status == GSTReturnStatus.NOT_FILED:
            compliance_status = GSTComplianceStatus.NON_COMPLIANT
        elif status == GSTReturnStatus.LATE_FILED:
            compliance_status = GSTComplianceStatus.PARTIALLY_COMPLIANT

        return GSTComplianceRecord(
            gstin=gstin,
            period=period,
            return_type=GSTReturnType.GSTR3B,
            status=status,
            compliance_status=compliance_status,
            due_date=due_date,
            filing_date=filing_date if status != GSTReturnStatus.NOT_FILED else None,
            days_delayed=days_delayed if status != GSTReturnStatus.NOT_FILED else None,
            penalty_amount=days_delayed * 100 if days_delayed > 0 else 0,
            interest_amount=days_delayed * 50 if days_delayed > 0 else 0,
            filing_score=100 - (days_delayed * 5) if status == GSTReturnStatus.FILED else 0,
            payment_score=random.uniform(80, 100),
            overall_score=random.uniform(70, 95)
        )

    def _generate_mock_payment_pattern(self, gstin: str, period: str) -> GSTPaymentPattern:
        """Generate mock payment pattern"""
        import random

        total_liability = random.uniform(50000, 500000)
        amount_paid = total_liability * random.uniform(0.8, 1.0)
        outstanding = max(0, total_liability - amount_paid)

        return GSTPaymentPattern(
            gstin=gstin,
            period=period,
            total_liability=total_liability,
            amount_paid=amount_paid,
            outstanding_amount=outstanding,
            payment_date=datetime.utcnow() - timedelta(days=random.randint(1, 30)),
            payment_method="Online Banking",
            avg_payment_delay=random.uniform(0, 10),
            payment_consistency=random.uniform(70, 95),
            cash_vs_itc_ratio=random.uniform(0.3, 0.8)
        )

    def _generate_period_list(self, from_period: str, to_period: str) -> List[str]:
        """Generate list of periods between from_period and to_period"""
        periods = []

        # Parse periods (MMYYYY format)
        from_month = int(from_period[:2])
        from_year = int(from_period[2:])
        to_month = int(to_period[:2])
        to_year = int(to_period[2:])

        current_date = datetime(from_year, from_month, 1)
        end_date = datetime(to_year, to_month, 1)

        while current_date <= end_date:
            period = f"{current_date.month:02d}{current_date.year}"
            periods.append(period)
            current_date += relativedelta(months=1)

        return periods

    # Storage methods
    async def _store_gst_turnover_data(self, data: GSTTurnoverData):
        """Store GST turnover data in Firestore"""
        try:
            doc_ref = self.db.collection('gst_turnover').document(f"{data.gstin}_{data.period}_{data.return_type}")
            doc_ref.set(data.dict())
        except Exception as e:
            logger.error(f"Error storing GST turnover data: {str(e)}")

    async def _store_gst_compliance_record(self, record: GSTComplianceRecord):
        """Store GST compliance record in Firestore"""
        try:
            doc_ref = self.db.collection('gst_compliance').document(f"{record.gstin}_{record.period}_{record.return_type}")
            doc_ref.set(record.dict())
        except Exception as e:
            logger.error(f"Error storing GST compliance record: {str(e)}")

    async def _store_gst_payment_pattern(self, pattern: GSTPaymentPattern):
        """Store GST payment pattern in Firestore"""
        try:
            doc_ref = self.db.collection('gst_payments').document(f"{pattern.gstin}_{pattern.period}")
            doc_ref.set(pattern.dict())
        except Exception as e:
            logger.error(f"Error storing GST payment pattern: {str(e)}")

    async def _store_gst_analytics(self, analytics: GSTAnalytics):
        """Store GST analytics in Firestore"""
        try:
            doc_ref = self.db.collection('gst_analytics').document(analytics.gstin)
            doc_ref.set(analytics.dict())
        except Exception as e:
            logger.error(f"Error storing GST analytics: {str(e)}")

    # Retrieval methods
    async def _get_stored_turnover_data(self, gstin: str) -> List[GSTTurnoverData]:
        """Get stored turnover data from Firestore"""
        try:
            docs = self.db.collection('gst_turnover').where('gstin', '==', gstin).stream()
            return [GSTTurnoverData(**doc.to_dict()) for doc in docs]
        except Exception as e:
            logger.error(f"Error retrieving GST turnover data: {str(e)}")
            return []

    async def _get_stored_compliance_records(self, gstin: str) -> List[GSTComplianceRecord]:
        """Get stored compliance records from Firestore"""
        try:
            docs = self.db.collection('gst_compliance').where('gstin', '==', gstin).stream()
            return [GSTComplianceRecord(**doc.to_dict()) for doc in docs]
        except Exception as e:
            logger.error(f"Error retrieving GST compliance records: {str(e)}")
            return []

    async def _get_stored_payment_patterns(self, gstin: str) -> List[GSTPaymentPattern]:
        """Get stored payment patterns from Firestore"""
        try:
            docs = self.db.collection('gst_payments').where('gstin', '==', gstin).stream()
            return [GSTPaymentPattern(**doc.to_dict()) for doc in docs]
        except Exception as e:
            logger.error(f"Error retrieving GST payment patterns: {str(e)}")
            return []

    # Additional calculation methods
    def _calculate_seasonal_index(self, turnover_data: List[GSTTurnoverData]) -> float:
        """Calculate seasonal variation index"""
        if len(turnover_data) < 12:
            return 0.0

        # Group by month and calculate average
        monthly_averages = {}
        for data in turnover_data:
            month = int(data.period[:2])
            if month not in monthly_averages:
                monthly_averages[month] = []
            monthly_averages[month].append(data.total_turnover)

        # Calculate coefficient of variation across months
        month_avgs = [sum(values) / len(values) for values in monthly_averages.values()]
        overall_avg = sum(month_avgs) / len(month_avgs)

        if overall_avg == 0:
            return 0.0

        variance = sum((x - overall_avg) ** 2 for x in month_avgs) / len(month_avgs)
        std_deviation = variance ** 0.5

        return round((std_deviation / overall_avg) * 100, 2)

    def _calculate_payment_compliance_rate(self, payment_patterns: List[GSTPaymentPattern]) -> float:
        """Calculate payment compliance rate"""
        if not payment_patterns:
            return 0.0

        compliant_payments = sum(1 for pattern in payment_patterns
                               if pattern.outstanding_amount <= pattern.total_liability * 0.05)  # 5% tolerance

        return (compliant_payments / len(payment_patterns)) * 100

    def _calculate_avg_filing_delay(self, compliance_records: List[GSTComplianceRecord]) -> float:
        """Calculate average filing delay"""
        if not compliance_records:
            return 0.0

        delays = [record.days_delayed for record in compliance_records
                 if record.days_delayed is not None]

        if not delays:
            return 0.0

        return sum(delays) / len(delays)

    def _calculate_avg_payment_delay(self, payment_patterns: List[GSTPaymentPattern]) -> float:
        """Calculate average payment delay"""
        if not payment_patterns:
            return 0.0

        delays = [pattern.avg_payment_delay for pattern in payment_patterns]
        return sum(delays) / len(delays)

    def _calculate_itc_utilization_rate(self, turnover_data: List[GSTTurnoverData]) -> float:
        """Calculate ITC utilization rate"""
        if not turnover_data:
            return 0.0

        total_claimed = sum(data.total_itc_claimed for data in turnover_data)
        total_utilized = sum(data.itc_utilized for data in turnover_data)

        if total_claimed == 0:
            return 0.0

        return (total_utilized / total_claimed) * 100

    def _calculate_itc_efficiency(self, turnover_data: List[GSTTurnoverData]) -> float:
        """Calculate ITC efficiency score"""
        utilization_rate = self._calculate_itc_utilization_rate(turnover_data)

        # Efficiency based on utilization rate and balance management
        if utilization_rate >= 90:
            return 100.0
        elif utilization_rate >= 80:
            return 85.0
        elif utilization_rate >= 70:
            return 70.0
        elif utilization_rate >= 60:
            return 55.0
        else:
            return 40.0

    def _calculate_avg_itc_balance(self, turnover_data: List[GSTTurnoverData]) -> float:
        """Calculate average ITC balance"""
        if not turnover_data:
            return 0.0

        balances = [data.itc_carried_forward for data in turnover_data]
        return sum(balances) / len(balances)

    def _calculate_compliance_risk_score(self, compliance_records: List[GSTComplianceRecord]) -> float:
        """Calculate compliance risk score (0-100, higher is riskier)"""
        if not compliance_records:
            return 50.0  # Medium risk for no data

        filing_rate = self._calculate_filing_compliance_rate(compliance_records)
        avg_delay = self._calculate_avg_filing_delay(compliance_records)

        # Risk increases with lower compliance and higher delays
        risk_score = 100 - filing_rate + (avg_delay * 2)
        return min(100, max(0, risk_score))

    def _calculate_payment_risk_score(self, payment_patterns: List[GSTPaymentPattern]) -> float:
        """Calculate payment risk score (0-100, higher is riskier)"""
        if not payment_patterns:
            return 50.0  # Medium risk for no data

        payment_rate = self._calculate_payment_compliance_rate(payment_patterns)
        avg_delay = self._calculate_avg_payment_delay(payment_patterns)

        # Risk increases with lower compliance and higher delays
        risk_score = 100 - payment_rate + (avg_delay * 3)
        return min(100, max(0, risk_score))

    def _determine_turnover_trend(self, turnover_data: List[GSTTurnoverData]) -> str:
        """Determine turnover trend"""
        growth_rate = self._calculate_turnover_growth_rate(turnover_data)

        if growth_rate > 5:
            return "increasing"
        elif growth_rate < -5:
            return "decreasing"
        else:
            return "stable"

    def _determine_compliance_trend(self, compliance_records: List[GSTComplianceRecord]) -> str:
        """Determine compliance trend"""
        if len(compliance_records) < 3:
            return "stable"

        # Check recent 3 months vs previous 3 months
        sorted_records = sorted(compliance_records, key=lambda x: x.period)
        recent_records = sorted_records[-3:]
        previous_records = sorted_records[-6:-3] if len(sorted_records) >= 6 else sorted_records[:-3]

        if not previous_records:
            return "stable"

        recent_score = sum(r.overall_score for r in recent_records) / len(recent_records)
        previous_score = sum(r.overall_score for r in previous_records) / len(previous_records)

        if recent_score > previous_score + 5:
            return "improving"
        elif recent_score < previous_score - 5:
            return "declining"
        else:
            return "stable"

    def _calculate_data_quality_score(self, turnover_data: List[GSTTurnoverData],
                                    compliance_records: List[GSTComplianceRecord]) -> float:
        """Calculate data quality and completeness score"""
        score = 0.0

        # Check data availability (40% weight)
        if turnover_data:
            score += 40

        if compliance_records:
            score += 30

        # Check data recency (30% weight)
        if turnover_data:
            latest_turnover = max(turnover_data, key=lambda x: x.period)
            days_old = (datetime.utcnow() - latest_turnover.created_at).days
            if days_old <= 30:
                score += 30
            elif days_old <= 60:
                score += 20
            elif days_old <= 90:
                score += 10

        return min(100, score)
