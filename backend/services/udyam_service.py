import asyncio
import aiohttp
import logging
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta, date
import re

from models.udyam_data import (
    UdyamRegistration, UdyamVerification, UdyamCompliance, UdyamAnalytics,
    UdyamDataRequest, UdyamDataResponse, UdyamHealthCheck,
    UdyamCategory, BusinessActivity, RegistrationStatus
)
from firebase.init import get_firestore_client

logger = logging.getLogger(__name__)

class UdyamService:
    """Service for Udyam registration data integration and verification"""
    
    def __init__(self):
        self.udyam_api_url = "https://udyamregistration.gov.in/api"  # Mock URL
        self.api_key = "your_udyam_api_key"  # From environment variables
        self.db = get_firestore_client()
        
    async def fetch_udyam_registration(self, udyam_number: str) -> Optional[UdyamRegistration]:
        """Fetch Udyam registration data"""
        try:
            # Validate Udyam number format
            if not self._validate_udyam_number(udyam_number):
                raise ValueError("Invalid Udyam number format")
            
            # In production, this would call the actual Udyam API
            registration_data = self._generate_mock_udyam_registration(udyam_number)
            
            # Store registration data
            await self._store_udyam_registration(registration_data)
            
            return registration_data
            
        except Exception as e:
            logger.error(f"Error fetching Udyam registration: {str(e)}")
            return None
    
    async def verify_udyam_registration(self, udyam_number: str, 
                                      enterprise_name: str, pan_number: str) -> UdyamVerification:
        """Verify Udyam registration details"""
        try:
            # Get stored registration data
            registration = await self._get_stored_udyam_registration(udyam_number)
            
            if not registration:
                # Try to fetch from API
                registration = await self.fetch_udyam_registration(udyam_number)
            
            if not registration:
                return UdyamVerification(
                    udyam_number=udyam_number,
                    verification_status="FAILED",
                    registration_valid=False,
                    overall_verification_score=0.0,
                    confidence_level="LOW",
                    verification_errors=["Registration not found"]
                )
            
            # Perform verification
            verification = self._perform_verification(registration, enterprise_name, pan_number)
            
            # Store verification result
            await self._store_udyam_verification(verification)
            
            return verification
            
        except Exception as e:
            logger.error(f"Error verifying Udyam registration: {str(e)}")
            return UdyamVerification(
                udyam_number=udyam_number,
                verification_status="ERROR",
                overall_verification_score=0.0,
                confidence_level="LOW",
                verification_errors=[str(e)]
            )
    
    async def fetch_udyam_compliance(self, udyam_number: str, year: int) -> Optional[UdyamCompliance]:
        """Fetch Udyam compliance data for a specific year"""
        try:
            # Mock compliance data - in production, fetch from API
            compliance_data = self._generate_mock_udyam_compliance(udyam_number, year)
            
            # Store compliance data
            await self._store_udyam_compliance(compliance_data)
            
            return compliance_data
            
        except Exception as e:
            logger.error(f"Error fetching Udyam compliance: {str(e)}")
            return None
    
    async def calculate_udyam_analytics(self, udyam_number: str, msme_id: str) -> Optional[UdyamAnalytics]:
        """Calculate comprehensive Udyam analytics"""
        try:
            # Get registration data
            registration = await self._get_stored_udyam_registration(udyam_number)
            if not registration:
                return None
            
            # Get compliance history
            compliance_records = await self._get_stored_compliance_records(udyam_number)
            
            # Get verification data
            verification = await self._get_stored_udyam_verification(udyam_number)
            
            # Calculate analytics
            analytics = UdyamAnalytics(
                udyam_number=udyam_number,
                msme_id=msme_id,
                registration_age_days=(datetime.utcnow() - registration.registration_date).days,
                registration_validity=registration.registration_status == RegistrationStatus.ACTIVE,
                category_stability=self._check_category_stability(compliance_records),
                compliance_history_score=self._calculate_compliance_history_score(compliance_records),
                filing_consistency=self._calculate_filing_consistency(compliance_records),
                data_accuracy_trend=self._determine_data_accuracy_trend(compliance_records),
                turnover_cagr=self._calculate_turnover_cagr(compliance_records),
                investment_growth_rate=self._calculate_investment_growth_rate(compliance_records),
                business_expansion_indicator=self._detect_business_expansion(compliance_records),
                identity_verification_score=verification.overall_verification_score if verification else 0.0,
                document_authenticity_score=self._calculate_document_authenticity_score(verification),
                fraud_risk_score=self._calculate_fraud_risk_score(registration, verification),
                category_appropriate=self._check_category_appropriateness(registration, compliance_records),
                upgrade_eligible=self._check_upgrade_eligibility(registration, compliance_records),
                downgrade_risk=self._check_downgrade_risk(registration, compliance_records),
                sector_performance_percentile=self._calculate_sector_percentile(registration),
                sector_growth_alignment=self._check_sector_growth_alignment(registration),
                registration_risk_flags=self._identify_risk_flags(registration, verification),
                compliance_risk_level=self._determine_compliance_risk_level(compliance_records),
                authenticity_risk_level=self._determine_authenticity_risk_level(verification),
                udyam_credibility_score=0.0,  # Will be calculated below
                business_legitimacy_score=0.0,  # Will be calculated below
                data_quality_score=self._calculate_data_quality_score(registration, compliance_records)
            )
            
            # Calculate composite scores
            analytics.udyam_credibility_score = self._calculate_credibility_score(analytics)
            analytics.business_legitimacy_score = self._calculate_legitimacy_score(analytics)
            
            # Store analytics
            await self._store_udyam_analytics(analytics)
            
            return analytics
            
        except Exception as e:
            logger.error(f"Error calculating Udyam analytics: {str(e)}")
            return None
    
    async def get_udyam_health_check(self, udyam_number: str) -> UdyamHealthCheck:
        """Get Udyam data source health check"""
        try:
            # Check last sync status
            last_sync_doc = self.db.collection('udyam_sync_status').document(udyam_number).get()
            
            if last_sync_doc.exists:
                sync_data = last_sync_doc.to_dict()
                return UdyamHealthCheck(
                    udyam_number=udyam_number,
                    last_sync=sync_data.get('last_sync', datetime.utcnow()),
                    sync_status=sync_data.get('sync_status', 'unknown'),
                    data_completeness=sync_data.get('data_completeness', 0.0),
                    api_response_time=sync_data.get('api_response_time', 0.0),
                    verification_status=sync_data.get('verification_status', 'PENDING'),
                    compliance_status=sync_data.get('compliance_status', 'UNKNOWN'),
                    error_count=sync_data.get('error_count', 0),
                    next_sync_scheduled=sync_data.get('next_sync_scheduled', datetime.utcnow() + timedelta(days=30))
                )
            else:
                return UdyamHealthCheck(
                    udyam_number=udyam_number,
                    last_sync=datetime.utcnow(),
                    sync_status='never_synced',
                    data_completeness=0.0,
                    api_response_time=0.0,
                    verification_status='PENDING',
                    compliance_status='UNKNOWN',
                    error_count=0,
                    next_sync_scheduled=datetime.utcnow() + timedelta(hours=1)
                )
                
        except Exception as e:
            logger.error(f"Error getting Udyam health check: {str(e)}")
            return UdyamHealthCheck(
                udyam_number=udyam_number,
                last_sync=datetime.utcnow(),
                sync_status='error',
                data_completeness=0.0,
                api_response_time=0.0,
                verification_status='ERROR',
                compliance_status='ERROR',
                error_count=1,
                next_sync_scheduled=datetime.utcnow() + timedelta(hours=1)
            )
    
    # Helper methods
    def _validate_udyam_number(self, udyam_number: str) -> bool:
        """Validate Udyam number format"""
        if not udyam_number:
            return False
        
        # Udyam number format: UDYAM-XX-00-0000000
        pattern = r'^UDYAM-[A-Z]{2}-\d{2}-\d{7}$'
        return bool(re.match(pattern, udyam_number))
    
    def _perform_verification(self, registration: UdyamRegistration, 
                            enterprise_name: str, pan_number: str) -> UdyamVerification:
        """Perform verification against registration data"""
        verification = UdyamVerification(
            udyam_number=registration.udyam_number,
            verification_status="SUCCESS"
        )
        
        # Name matching
        verification.name_match_score = self._calculate_name_match_score(
            registration.enterprise_name, enterprise_name
        )
        
        # PAN matching
        verification.pan_match = registration.pan_number.upper() == pan_number.upper()
        
        # Status check
        verification.status_active = registration.registration_status == RegistrationStatus.ACTIVE
        verification.registration_valid = verification.status_active
        
        # Details match
        verification.details_match = (
            verification.name_match_score > 80 and 
            verification.pan_match and 
            verification.status_active
        )
        
        # Calculate overall score
        score_components = [
            verification.name_match_score,
            100 if verification.pan_match else 0,
            100 if verification.status_active else 0,
            100 if registration.aadhaar_verified else 50,
            100 if registration.pan_verified else 50
        ]
        
        verification.overall_verification_score = sum(score_components) / len(score_components)
        
        # Determine confidence level
        if verification.overall_verification_score >= 90:
            verification.confidence_level = "HIGH"
        elif verification.overall_verification_score >= 70:
            verification.confidence_level = "MEDIUM"
        else:
            verification.confidence_level = "LOW"
        
        # Add warnings/errors
        if verification.name_match_score < 80:
            verification.verification_warnings.append("Name match score is low")
        
        if not verification.pan_match:
            verification.verification_errors.append("PAN number does not match")
        
        if not verification.status_active:
            verification.verification_errors.append("Registration is not active")
        
        return verification
    
    def _calculate_name_match_score(self, name1: str, name2: str) -> float:
        """Calculate name matching score using simple string similarity"""
        from difflib import SequenceMatcher
        
        # Normalize names
        name1_norm = re.sub(r'[^a-zA-Z0-9\s]', '', name1.lower().strip())
        name2_norm = re.sub(r'[^a-zA-Z0-9\s]', '', name2.lower().strip())
        
        # Calculate similarity
        similarity = SequenceMatcher(None, name1_norm, name2_norm).ratio()
        return similarity * 100
    
    def _calculate_credibility_score(self, analytics: UdyamAnalytics) -> float:
        """Calculate overall Udyam credibility score"""
        weights = {
            'registration_validity': 0.25,
            'compliance_history': 0.25,
            'identity_verification': 0.20,
            'document_authenticity': 0.15,
            'fraud_risk': 0.15
        }
        
        registration_score = 100 if analytics.registration_validity else 0
        compliance_score = analytics.compliance_history_score
        identity_score = analytics.identity_verification_score
        document_score = analytics.document_authenticity_score
        fraud_score = 100 - analytics.fraud_risk_score  # Inverse of fraud risk
        
        credibility_score = (
            registration_score * weights['registration_validity'] +
            compliance_score * weights['compliance_history'] +
            identity_score * weights['identity_verification'] +
            document_score * weights['document_authenticity'] +
            fraud_score * weights['fraud_risk']
        )
        
        return round(credibility_score, 2)
    
    def _calculate_legitimacy_score(self, analytics: UdyamAnalytics) -> float:
        """Calculate business legitimacy score"""
        legitimacy_factors = []
        
        # Registration age (older is more legitimate)
        if analytics.registration_age_days > 365:
            legitimacy_factors.append(90)
        elif analytics.registration_age_days > 180:
            legitimacy_factors.append(70)
        else:
            legitimacy_factors.append(50)
        
        # Category appropriateness
        legitimacy_factors.append(90 if analytics.category_appropriate else 30)
        
        # Filing consistency
        legitimacy_factors.append(analytics.filing_consistency)
        
        # Business expansion indicator
        legitimacy_factors.append(80 if analytics.business_expansion_indicator else 60)
        
        # No risk flags
        risk_penalty = len(analytics.registration_risk_flags) * 10
        legitimacy_factors.append(max(0, 100 - risk_penalty))
        
        return sum(legitimacy_factors) / len(legitimacy_factors)

    # Mock data generation methods
    def _generate_mock_udyam_registration(self, udyam_number: str) -> UdyamRegistration:
        """Generate mock Udyam registration data"""
        import random
        from datetime import date

        states = ["Maharashtra", "Karnataka", "Tamil Nadu", "Gujarat", "Delhi", "Uttar Pradesh"]
        business_names = ["Tech Solutions", "Manufacturing Co", "Trading House", "Service Provider", "Industries Ltd"]

        return UdyamRegistration(
            udyam_number=udyam_number,
            msme_id=f"MSME_{udyam_number.split('-')[-1]}",
            enterprise_name=f"{random.choice(business_names)} {random.randint(1, 999)}",
            enterprise_type="Private Limited Company",
            date_of_incorporation=date(2020, random.randint(1, 12), random.randint(1, 28)),
            date_of_commencement=date(2020, random.randint(1, 12), random.randint(1, 28)),
            udyam_category=random.choice(list(UdyamCategory)),
            business_activity=random.choice(list(BusinessActivity)),
            nic_code=f"{random.randint(10000, 99999)}",
            nic_description="Manufacturing of electronic components",
            investment_in_plant_machinery=random.uniform(100000, 5000000),
            investment_in_equipment=random.uniform(50000, 2000000),
            total_investment=random.uniform(200000, 7000000),
            previous_year_turnover=random.uniform(500000, 10000000),
            state=random.choice(states),
            district=f"District {random.randint(1, 50)}",
            address=f"Plot {random.randint(1, 999)}, Industrial Area",
            pincode=f"{random.randint(100000, 999999)}",
            mobile_number=f"9{random.randint(100000000, 999999999)}",
            email_id=f"business{random.randint(1, 999)}@example.com",
            aadhaar_number=f"XXXX-XXXX-{random.randint(1000, 9999)}",
            pan_number=f"ABCDE{random.randint(1000, 9999)}F",
            aadhaar_verified=random.choice([True, False]),
            pan_verified=True,
            registration_date=datetime.utcnow() - timedelta(days=random.randint(30, 1000)),
            registration_status=RegistrationStatus.ACTIVE,
            validity_date=datetime.utcnow() + timedelta(days=365),
            bank_name="State Bank of India",
            bank_account_number=f"XXXX{random.randint(1000, 9999)}",
            ifsc_code=f"SBIN0{random.randint(100000, 999999)}",
            is_women_owned=random.choice([True, False]),
            is_sc_st_owned=random.choice([True, False]),
            is_physically_handicapped=False,
            is_minority_owned=random.choice([True, False]),
            annual_return_filed=random.choice([True, False]),
            last_annual_return_date=date(2023, 12, 31) if random.choice([True, False]) else None
        )

    def _generate_mock_udyam_compliance(self, udyam_number: str, year: int) -> UdyamCompliance:
        """Generate mock Udyam compliance data"""
        import random

        due_date = date(year + 1, 3, 31)  # Annual return due by March 31
        filed = random.choice([True, False])
        filing_date = due_date + timedelta(days=random.randint(-30, 60)) if filed else None

        return UdyamCompliance(
            udyam_number=udyam_number,
            compliance_period=str(year),
            annual_return_due_date=due_date,
            annual_return_filed=filed,
            annual_return_filing_date=filing_date,
            days_delayed=max(0, (filing_date - due_date).days) if filing_date else None,
            declared_turnover=random.uniform(500000, 8000000),
            turnover_growth=random.uniform(-20, 50),
            category_compliance=True,
            declared_investment=random.uniform(200000, 5000000),
            investment_growth=random.uniform(-10, 30),
            investment_compliance=True,
            filing_compliance_score=random.uniform(70, 100) if filed else random.uniform(0, 40),
            data_accuracy_score=random.uniform(80, 100),
            overall_compliance_score=random.uniform(60, 95),
            penalty_amount=random.uniform(0, 5000) if not filed or (filing_date and filing_date > due_date) else 0,
            warning_issued=random.choice([True, False]),
            action_taken="Warning issued" if random.choice([True, False]) else None
        )

    # Storage and retrieval methods
    async def _store_udyam_registration(self, registration: UdyamRegistration):
        """Store Udyam registration in Firestore"""
        try:
            doc_ref = self.db.collection('udyam_registrations').document(registration.udyam_number)
            doc_ref.set(registration.dict())
        except Exception as e:
            logger.error(f"Error storing Udyam registration: {str(e)}")

    async def _store_udyam_verification(self, verification: UdyamVerification):
        """Store Udyam verification in Firestore"""
        try:
            doc_ref = self.db.collection('udyam_verifications').document(verification.udyam_number)
            doc_ref.set(verification.dict())
        except Exception as e:
            logger.error(f"Error storing Udyam verification: {str(e)}")

    async def _store_udyam_compliance(self, compliance: UdyamCompliance):
        """Store Udyam compliance in Firestore"""
        try:
            doc_id = f"{compliance.udyam_number}_{compliance.compliance_period}"
            doc_ref = self.db.collection('udyam_compliance').document(doc_id)
            doc_ref.set(compliance.dict())
        except Exception as e:
            logger.error(f"Error storing Udyam compliance: {str(e)}")

    async def _store_udyam_analytics(self, analytics: UdyamAnalytics):
        """Store Udyam analytics in Firestore"""
        try:
            doc_ref = self.db.collection('udyam_analytics').document(analytics.udyam_number)
            doc_ref.set(analytics.dict())
        except Exception as e:
            logger.error(f"Error storing Udyam analytics: {str(e)}")

    async def _get_stored_udyam_registration(self, udyam_number: str) -> Optional[UdyamRegistration]:
        """Get stored Udyam registration from Firestore"""
        try:
            doc = self.db.collection('udyam_registrations').document(udyam_number).get()
            if doc.exists:
                return UdyamRegistration(**doc.to_dict())
            return None
        except Exception as e:
            logger.error(f"Error retrieving Udyam registration: {str(e)}")
            return None

    async def _get_stored_udyam_verification(self, udyam_number: str) -> Optional[UdyamVerification]:
        """Get stored Udyam verification from Firestore"""
        try:
            doc = self.db.collection('udyam_verifications').document(udyam_number).get()
            if doc.exists:
                return UdyamVerification(**doc.to_dict())
            return None
        except Exception as e:
            logger.error(f"Error retrieving Udyam verification: {str(e)}")
            return None

    async def _get_stored_compliance_records(self, udyam_number: str) -> List[UdyamCompliance]:
        """Get stored compliance records from Firestore"""
        try:
            docs = self.db.collection('udyam_compliance').where('udyam_number', '==', udyam_number).stream()
            return [UdyamCompliance(**doc.to_dict()) for doc in docs]
        except Exception as e:
            logger.error(f"Error retrieving Udyam compliance records: {str(e)}")
            return []

    # Additional calculation methods
    def _check_category_stability(self, compliance_records: List[UdyamCompliance]) -> bool:
        """Check if MSME category has been stable"""
        # Simplified check - in practice, would analyze turnover/investment trends
        return len(compliance_records) > 0 and all(r.category_compliance for r in compliance_records)

    def _calculate_compliance_history_score(self, compliance_records: List[UdyamCompliance]) -> float:
        """Calculate compliance history score"""
        if not compliance_records:
            return 0.0

        scores = [record.overall_compliance_score for record in compliance_records]
        return sum(scores) / len(scores)

    def _calculate_filing_consistency(self, compliance_records: List[UdyamCompliance]) -> float:
        """Calculate filing consistency score"""
        if not compliance_records:
            return 0.0

        filed_on_time = sum(1 for record in compliance_records
                           if record.annual_return_filed and
                           (record.days_delayed is None or record.days_delayed <= 0))

        return (filed_on_time / len(compliance_records)) * 100

    def _determine_data_accuracy_trend(self, compliance_records: List[UdyamCompliance]) -> str:
        """Determine data accuracy trend"""
        if len(compliance_records) < 2:
            return "stable"

        # Sort by period and check trend
        sorted_records = sorted(compliance_records, key=lambda x: x.compliance_period)
        recent_score = sorted_records[-1].data_accuracy_score
        previous_score = sorted_records[-2].data_accuracy_score

        if recent_score > previous_score + 5:
            return "improving"
        elif recent_score < previous_score - 5:
            return "declining"
        else:
            return "stable"

    def _calculate_turnover_cagr(self, compliance_records: List[UdyamCompliance]) -> float:
        """Calculate turnover CAGR"""
        if len(compliance_records) < 2:
            return 0.0

        sorted_records = sorted(compliance_records, key=lambda x: x.compliance_period)
        first_turnover = sorted_records[0].declared_turnover
        last_turnover = sorted_records[-1].declared_turnover
        years = len(sorted_records) - 1

        if first_turnover <= 0 or years <= 0:
            return 0.0

        cagr = ((last_turnover / first_turnover) ** (1 / years) - 1) * 100
        return round(cagr, 2)

    def _calculate_investment_growth_rate(self, compliance_records: List[UdyamCompliance]) -> float:
        """Calculate investment growth rate"""
        if len(compliance_records) < 2:
            return 0.0

        sorted_records = sorted(compliance_records, key=lambda x: x.compliance_period)
        growth_rates = [record.investment_growth for record in sorted_records if record.investment_growth is not None]

        if not growth_rates:
            return 0.0

        return sum(growth_rates) / len(growth_rates)

    def _detect_business_expansion(self, compliance_records: List[UdyamCompliance]) -> bool:
        """Detect business expansion indicators"""
        if len(compliance_records) < 2:
            return False

        # Check for consistent growth in turnover and investment
        recent_records = sorted(compliance_records, key=lambda x: x.compliance_period)[-2:]

        turnover_growth = recent_records[-1].turnover_growth > 10
        investment_growth = recent_records[-1].investment_growth > 5

        return turnover_growth and investment_growth

    def _calculate_document_authenticity_score(self, verification: Optional[UdyamVerification]) -> float:
        """Calculate document authenticity score"""
        if not verification:
            return 0.0

        authenticity_factors = []

        # Verification score
        authenticity_factors.append(verification.overall_verification_score)

        # Identity verification
        if verification.pan_match:
            authenticity_factors.append(90)
        else:
            authenticity_factors.append(30)

        if verification.aadhaar_match:
            authenticity_factors.append(85)
        else:
            authenticity_factors.append(50)

        # No suspicious activity
        if not verification.suspicious_activity:
            authenticity_factors.append(80)
        else:
            authenticity_factors.append(20)

        return sum(authenticity_factors) / len(authenticity_factors)

    def _calculate_fraud_risk_score(self, registration: UdyamRegistration,
                                  verification: Optional[UdyamVerification]) -> float:
        """Calculate fraud risk score"""
        risk_score = 0.0

        # Registration age (newer registrations have higher risk)
        age_days = (datetime.utcnow() - registration.registration_date).days
        if age_days < 30:
            risk_score += 30
        elif age_days < 90:
            risk_score += 15
        elif age_days < 180:
            risk_score += 5

        # Verification issues
        if verification:
            if verification.multiple_registrations:
                risk_score += 25
            if verification.suspicious_activity:
                risk_score += 35
            if verification.blacklisted:
                risk_score += 50
            if verification.overall_verification_score < 50:
                risk_score += 20

        # Identity verification
        if not registration.aadhaar_verified:
            risk_score += 15
        if not registration.pan_verified:
            risk_score += 20

        return min(100, risk_score)

    def _check_category_appropriateness(self, registration: UdyamRegistration,
                                      compliance_records: List[UdyamCompliance]) -> bool:
        """Check if current category is appropriate based on turnover/investment"""
        if not compliance_records:
            return True

        latest_compliance = max(compliance_records, key=lambda x: x.compliance_period)

        # Simplified category check based on turnover
        if registration.udyam_category == UdyamCategory.MICRO:
            return latest_compliance.declared_turnover <= 5000000  # 50 Lakh
        elif registration.udyam_category == UdyamCategory.SMALL:
            return 5000000 < latest_compliance.declared_turnover <= 50000000  # 5 Crore
        elif registration.udyam_category == UdyamCategory.MEDIUM:
            return 50000000 < latest_compliance.declared_turnover <= 250000000  # 25 Crore

        return True

    def _check_upgrade_eligibility(self, registration: UdyamRegistration,
                                 compliance_records: List[UdyamCompliance]) -> bool:
        """Check if eligible for category upgrade"""
        if not compliance_records:
            return False

        latest_compliance = max(compliance_records, key=lambda x: x.compliance_period)

        # Check if consistently exceeding current category limits
        if registration.udyam_category == UdyamCategory.MICRO:
            return latest_compliance.declared_turnover > 5000000
        elif registration.udyam_category == UdyamCategory.SMALL:
            return latest_compliance.declared_turnover > 50000000

        return False

    def _check_downgrade_risk(self, registration: UdyamRegistration,
                            compliance_records: List[UdyamCompliance]) -> bool:
        """Check risk of category downgrade"""
        if not compliance_records or len(compliance_records) < 2:
            return False

        # Check for declining turnover trend
        sorted_records = sorted(compliance_records, key=lambda x: x.compliance_period)
        recent_records = sorted_records[-2:]

        return recent_records[-1].turnover_growth < -20  # 20% decline

    def _calculate_sector_percentile(self, registration: UdyamRegistration) -> float:
        """Calculate performance percentile in sector"""
        # Mock calculation - in practice, would compare with sector data
        import random
        return random.uniform(25, 85)

    def _check_sector_growth_alignment(self, registration: UdyamRegistration) -> bool:
        """Check if business is aligned with sector growth"""
        # Mock check - in practice, would analyze sector trends
        import random
        return random.choice([True, False])

    def _identify_risk_flags(self, registration: UdyamRegistration,
                           verification: Optional[UdyamVerification]) -> List[str]:
        """Identify risk flags"""
        flags = []

        # Registration age
        age_days = (datetime.utcnow() - registration.registration_date).days
        if age_days < 30:
            flags.append("Very new registration")

        # Verification issues
        if verification:
            if verification.overall_verification_score < 70:
                flags.append("Low verification score")
            if verification.multiple_registrations:
                flags.append("Multiple registrations found")
            if verification.suspicious_activity:
                flags.append("Suspicious activity detected")

        # Identity verification
        if not registration.aadhaar_verified:
            flags.append("Aadhaar not verified")
        if not registration.pan_verified:
            flags.append("PAN not verified")

        # Compliance
        if not registration.annual_return_filed:
            flags.append("Annual return not filed")

        return flags

    def _determine_compliance_risk_level(self, compliance_records: List[UdyamCompliance]) -> str:
        """Determine compliance risk level"""
        if not compliance_records:
            return "MEDIUM"

        avg_compliance_score = sum(r.overall_compliance_score for r in compliance_records) / len(compliance_records)

        if avg_compliance_score >= 80:
            return "LOW"
        elif avg_compliance_score >= 60:
            return "MEDIUM"
        else:
            return "HIGH"

    def _determine_authenticity_risk_level(self, verification: Optional[UdyamVerification]) -> str:
        """Determine authenticity risk level"""
        if not verification:
            return "HIGH"

        if verification.overall_verification_score >= 85:
            return "LOW"
        elif verification.overall_verification_score >= 65:
            return "MEDIUM"
        else:
            return "HIGH"

    def _calculate_data_quality_score(self, registration: UdyamRegistration,
                                    compliance_records: List[UdyamCompliance]) -> float:
        """Calculate data quality score"""
        quality_factors = []

        # Registration completeness
        completeness_score = 0
        if registration.enterprise_name:
            completeness_score += 20
        if registration.pan_number:
            completeness_score += 20
        if registration.aadhaar_number:
            completeness_score += 15
        if registration.bank_account_number:
            completeness_score += 15
        if registration.email_id:
            completeness_score += 10
        if registration.mobile_number:
            completeness_score += 10
        if registration.address:
            completeness_score += 10

        quality_factors.append(completeness_score)

        # Verification status
        verification_score = 0
        if registration.aadhaar_verified:
            verification_score += 50
        if registration.pan_verified:
            verification_score += 50

        quality_factors.append(verification_score)

        # Compliance data availability
        if compliance_records:
            quality_factors.append(80)
        else:
            quality_factors.append(20)

        # Data recency
        days_since_update = (datetime.utcnow() - registration.updated_at).days
        if days_since_update <= 30:
            quality_factors.append(90)
        elif days_since_update <= 90:
            quality_factors.append(70)
        else:
            quality_factors.append(40)

        return sum(quality_factors) / len(quality_factors)
