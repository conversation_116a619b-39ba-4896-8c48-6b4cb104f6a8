import asyncio
import logging
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from enum import Enum
import json

from models.gst_data import GSTDataRequest
from models.account_aggregator import AADataRequest
from models.udyam_data import UdyamDataRequest
from models.cash_flow import CashFlowRequest
from models.financial_metrics import FinancialMetricsRequest

from services.gst_service import GSTService
from services.account_aggregator_service import AccountAggregatorService
from services.udyam_service import UdyamService
from services.cash_flow_service import CashFlowService
from services.financial_metrics_service import FinancialMetricsService
from services.compliance_service import ComplianceService

from firebase.init import get_firestore_client

logger = logging.getLogger(__name__)

class RefreshStatus(str, Enum):
    PENDING = "PENDING"
    IN_PROGRESS = "IN_PROGRESS"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"
    CANCELLED = "CANCELLED"

class RefreshPriority(str, Enum):
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"
    CRITICAL = "CRITICAL"

class DataSource(str, Enum):
    GST = "GST"
    ACCOUNT_AGGREGATOR = "ACCOUNT_AGGREGATOR"
    UDYAM = "UDYAM"
    CASH_FLOW = "CASH_FLOW"
    FINANCIAL_METRICS = "FINANCIAL_METRICS"
    COMPLIANCE = "COMPLIANCE"

class RefreshJob:
    """Data refresh job model"""
    def __init__(self, job_id: str, msme_id: str, data_source: DataSource, 
                 priority: RefreshPriority = RefreshPriority.MEDIUM):
        self.job_id = job_id
        self.msme_id = msme_id
        self.data_source = data_source
        self.priority = priority
        self.status = RefreshStatus.PENDING
        self.created_at = datetime.utcnow()
        self.started_at: Optional[datetime] = None
        self.completed_at: Optional[datetime] = None
        self.error_message: Optional[str] = None
        self.retry_count = 0
        self.max_retries = 3
        self.progress = 0.0
        self.metadata: Dict[str, Any] = {}

class DataRefreshService:
    """Service for managing real-time data refresh operations"""
    
    def __init__(self):
        self.db = get_firestore_client()
        self.job_queue: List[RefreshJob] = []
        self.active_jobs: Dict[str, RefreshJob] = {}
        self.max_concurrent_jobs = 5
        self.refresh_intervals = {
            DataSource.GST: timedelta(hours=6),
            DataSource.ACCOUNT_AGGREGATOR: timedelta(hours=2),
            DataSource.UDYAM: timedelta(days=1),
            DataSource.CASH_FLOW: timedelta(hours=4),
            DataSource.FINANCIAL_METRICS: timedelta(hours=8),
            DataSource.COMPLIANCE: timedelta(days=1)
        }
        
        # Initialize services
        self.gst_service = GSTService()
        self.aa_service = AccountAggregatorService()
        self.udyam_service = UdyamService()
        self.cash_flow_service = CashFlowService()
        self.financial_metrics_service = FinancialMetricsService()
        self.compliance_service = ComplianceService()

        # Background tasks will be started when needed
        self._queue_task = None
        self._scheduler_task = None

    def _ensure_background_tasks(self):
        """Ensure background tasks are running"""
        try:
            if self._queue_task is None or self._queue_task.done():
                self._queue_task = asyncio.create_task(self._process_refresh_queue())
            if self._scheduler_task is None or self._scheduler_task.done():
                self._scheduler_task = asyncio.create_task(self._schedule_automatic_refreshes())
        except RuntimeError:
            # No event loop running, tasks will be started later
            pass

    async def schedule_refresh(self, msme_id: str, data_sources: List[DataSource],
                             priority: RefreshPriority = RefreshPriority.MEDIUM) -> List[str]:
        """Schedule data refresh for specified sources"""
        # Ensure background tasks are running
        self._ensure_background_tasks()

        try:
            job_ids = []
            
            for data_source in data_sources:
                job_id = f"{data_source.value}_{msme_id}_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"
                job = RefreshJob(job_id, msme_id, data_source, priority)
                
                # Add to queue based on priority
                self._add_job_to_queue(job)
                job_ids.append(job_id)
                
                # Store job in database
                await self._store_refresh_job(job)
                
                logger.info(f"Scheduled refresh job {job_id} for MSME {msme_id}")
            
            return job_ids
            
        except Exception as e:
            logger.error(f"Error scheduling refresh: {str(e)}")
            raise
    
    async def schedule_comprehensive_refresh(self, msme_id: str, 
                                           priority: RefreshPriority = RefreshPriority.MEDIUM) -> List[str]:
        """Schedule comprehensive refresh for all data sources"""
        all_sources = [
            DataSource.GST,
            DataSource.ACCOUNT_AGGREGATOR,
            DataSource.UDYAM,
            DataSource.CASH_FLOW,
            DataSource.FINANCIAL_METRICS,
            DataSource.COMPLIANCE
        ]
        
        return await self.schedule_refresh(msme_id, all_sources, priority)
    
    async def get_refresh_status(self, job_id: str) -> Optional[RefreshJob]:
        """Get status of a refresh job"""
        try:
            # Check active jobs first
            if job_id in self.active_jobs:
                return self.active_jobs[job_id]
            
            # Check database
            doc = self.db.collection('refresh_jobs').document(job_id).get()
            if doc.exists:
                data = doc.to_dict()
                job = RefreshJob(data['job_id'], data['msme_id'], DataSource(data['data_source']))
                job.status = RefreshStatus(data['status'])
                job.created_at = data['created_at']
                job.started_at = data.get('started_at')
                job.completed_at = data.get('completed_at')
                job.error_message = data.get('error_message')
                job.retry_count = data.get('retry_count', 0)
                job.progress = data.get('progress', 0.0)
                job.metadata = data.get('metadata', {})
                return job
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting refresh status: {str(e)}")
            return None
    
    async def cancel_refresh_job(self, job_id: str) -> bool:
        """Cancel a refresh job"""
        try:
            # Check if job is active
            if job_id in self.active_jobs:
                job = self.active_jobs[job_id]
                job.status = RefreshStatus.CANCELLED
                job.completed_at = datetime.utcnow()
                await self._store_refresh_job(job)
                del self.active_jobs[job_id]
                return True
            
            # Check if job is in queue
            for i, job in enumerate(self.job_queue):
                if job.job_id == job_id:
                    job.status = RefreshStatus.CANCELLED
                    job.completed_at = datetime.utcnow()
                    await self._store_refresh_job(job)
                    self.job_queue.pop(i)
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error cancelling refresh job: {str(e)}")
            return False
    
    async def get_msme_refresh_status(self, msme_id: str) -> Dict[str, Any]:
        """Get comprehensive refresh status for an MSME"""
        try:
            # Get all jobs for this MSME
            docs = self.db.collection('refresh_jobs').where('msme_id', '==', msme_id).order_by('created_at', direction='DESCENDING').limit(50).stream()
            
            jobs_by_source = {}
            recent_jobs = []
            
            for doc in docs:
                data = doc.to_dict()
                recent_jobs.append(data)
                
                source = data['data_source']
                if source not in jobs_by_source or data['created_at'] > jobs_by_source[source]['created_at']:
                    jobs_by_source[source] = data
            
            # Calculate overall status
            overall_status = "HEALTHY"
            last_refresh_times = {}
            next_refresh_times = {}
            
            for source, interval in self.refresh_intervals.items():
                source_key = source.value
                if source_key in jobs_by_source:
                    last_job = jobs_by_source[source_key]
                    last_refresh_times[source_key] = last_job.get('completed_at', last_job['created_at'])
                    
                    # Calculate next refresh time
                    if last_job.get('completed_at'):
                        next_refresh = last_job['completed_at'] + interval
                        next_refresh_times[source_key] = next_refresh
                        
                        # Check if refresh is overdue
                        if datetime.utcnow() > next_refresh:
                            overall_status = "NEEDS_REFRESH"
                else:
                    overall_status = "NEEDS_INITIAL_REFRESH"
            
            return {
                "msme_id": msme_id,
                "overall_status": overall_status,
                "last_refresh_times": last_refresh_times,
                "next_refresh_times": next_refresh_times,
                "jobs_by_source": jobs_by_source,
                "recent_jobs": recent_jobs[:10],  # Last 10 jobs
                "active_jobs": [job.job_id for job in self.active_jobs.values() if job.msme_id == msme_id],
                "queued_jobs": [job.job_id for job in self.job_queue if job.msme_id == msme_id]
            }
            
        except Exception as e:
            logger.error(f"Error getting MSME refresh status: {str(e)}")
            return {"error": str(e)}
    
    async def get_system_health(self) -> Dict[str, Any]:
        """Get overall system health and refresh statistics"""
        try:
            # Get recent job statistics
            since = datetime.utcnow() - timedelta(hours=24)
            docs = self.db.collection('refresh_jobs').where('created_at', '>=', since).stream()
            
            total_jobs = 0
            completed_jobs = 0
            failed_jobs = 0
            jobs_by_source = {}
            
            for doc in docs:
                data = doc.to_dict()
                total_jobs += 1
                
                status = data['status']
                if status == RefreshStatus.COMPLETED.value:
                    completed_jobs += 1
                elif status == RefreshStatus.FAILED.value:
                    failed_jobs += 1
                
                source = data['data_source']
                if source not in jobs_by_source:
                    jobs_by_source[source] = {"total": 0, "completed": 0, "failed": 0}
                
                jobs_by_source[source]["total"] += 1
                if status == RefreshStatus.COMPLETED.value:
                    jobs_by_source[source]["completed"] += 1
                elif status == RefreshStatus.FAILED.value:
                    jobs_by_source[source]["failed"] += 1
            
            # Calculate success rate
            success_rate = (completed_jobs / total_jobs * 100) if total_jobs > 0 else 0
            
            # Get queue status
            queue_status = {
                "total_queued": len(self.job_queue),
                "active_jobs": len(self.active_jobs),
                "by_priority": {
                    "CRITICAL": len([j for j in self.job_queue if j.priority == RefreshPriority.CRITICAL]),
                    "HIGH": len([j for j in self.job_queue if j.priority == RefreshPriority.HIGH]),
                    "MEDIUM": len([j for j in self.job_queue if j.priority == RefreshPriority.MEDIUM]),
                    "LOW": len([j for j in self.job_queue if j.priority == RefreshPriority.LOW])
                }
            }
            
            return {
                "system_status": "HEALTHY" if success_rate >= 90 else "DEGRADED" if success_rate >= 70 else "UNHEALTHY",
                "last_24_hours": {
                    "total_jobs": total_jobs,
                    "completed_jobs": completed_jobs,
                    "failed_jobs": failed_jobs,
                    "success_rate": round(success_rate, 2)
                },
                "jobs_by_source": jobs_by_source,
                "queue_status": queue_status,
                "refresh_intervals": {source.value: str(interval) for source, interval in self.refresh_intervals.items()},
                "last_updated": datetime.utcnow()
            }
            
        except Exception as e:
            logger.error(f"Error getting system health: {str(e)}")
            return {"error": str(e)}
    
    # Private methods
    def _add_job_to_queue(self, job: RefreshJob):
        """Add job to queue based on priority"""
        priority_order = {
            RefreshPriority.CRITICAL: 0,
            RefreshPriority.HIGH: 1,
            RefreshPriority.MEDIUM: 2,
            RefreshPriority.LOW: 3
        }
        
        # Insert job in priority order
        inserted = False
        for i, existing_job in enumerate(self.job_queue):
            if priority_order[job.priority] < priority_order[existing_job.priority]:
                self.job_queue.insert(i, job)
                inserted = True
                break
        
        if not inserted:
            self.job_queue.append(job)
    
    async def _process_refresh_queue(self):
        """Background task to process refresh queue"""
        while True:
            try:
                # Check if we can process more jobs
                if len(self.active_jobs) < self.max_concurrent_jobs and self.job_queue:
                    job = self.job_queue.pop(0)
                    
                    # Start processing job
                    self.active_jobs[job.job_id] = job
                    asyncio.create_task(self._execute_refresh_job(job))
                
                # Wait before checking again
                await asyncio.sleep(5)
                
            except Exception as e:
                logger.error(f"Error in refresh queue processor: {str(e)}")
                await asyncio.sleep(10)
    
    async def _execute_refresh_job(self, job: RefreshJob):
        """Execute a refresh job"""
        try:
            job.status = RefreshStatus.IN_PROGRESS
            job.started_at = datetime.utcnow()
            await self._store_refresh_job(job)
            
            # Execute based on data source
            if job.data_source == DataSource.GST:
                await self._refresh_gst_data(job)
            elif job.data_source == DataSource.ACCOUNT_AGGREGATOR:
                await self._refresh_aa_data(job)
            elif job.data_source == DataSource.UDYAM:
                await self._refresh_udyam_data(job)
            elif job.data_source == DataSource.CASH_FLOW:
                await self._refresh_cash_flow_data(job)
            elif job.data_source == DataSource.FINANCIAL_METRICS:
                await self._refresh_financial_metrics_data(job)
            elif job.data_source == DataSource.COMPLIANCE:
                await self._refresh_compliance_data(job)
            
            job.status = RefreshStatus.COMPLETED
            job.completed_at = datetime.utcnow()
            job.progress = 100.0
            
        except Exception as e:
            logger.error(f"Error executing refresh job {job.job_id}: {str(e)}")
            job.status = RefreshStatus.FAILED
            job.error_message = str(e)
            job.completed_at = datetime.utcnow()
            
            # Retry logic
            if job.retry_count < job.max_retries:
                job.retry_count += 1
                job.status = RefreshStatus.PENDING
                job.started_at = None
                job.completed_at = None
                
                # Add back to queue with delay
                await asyncio.sleep(60 * job.retry_count)  # Exponential backoff
                self._add_job_to_queue(job)
        
        finally:
            await self._store_refresh_job(job)
            if job.job_id in self.active_jobs:
                del self.active_jobs[job.job_id]
    
    async def _refresh_gst_data(self, job: RefreshJob):
        """Refresh GST data for an MSME"""
        # Implementation would call GST service
        job.progress = 50.0
        await asyncio.sleep(2)  # Simulate processing
        job.progress = 100.0
    
    async def _refresh_aa_data(self, job: RefreshJob):
        """Refresh Account Aggregator data for an MSME"""
        # Implementation would call AA service
        job.progress = 50.0
        await asyncio.sleep(3)  # Simulate processing
        job.progress = 100.0
    
    async def _refresh_udyam_data(self, job: RefreshJob):
        """Refresh Udyam data for an MSME"""
        # Implementation would call Udyam service
        job.progress = 50.0
        await asyncio.sleep(1)  # Simulate processing
        job.progress = 100.0
    
    async def _refresh_cash_flow_data(self, job: RefreshJob):
        """Refresh cash flow data for an MSME"""
        # Implementation would call cash flow service
        job.progress = 50.0
        await asyncio.sleep(4)  # Simulate processing
        job.progress = 100.0
    
    async def _refresh_financial_metrics_data(self, job: RefreshJob):
        """Refresh financial metrics for an MSME"""
        # Implementation would call financial metrics service
        job.progress = 50.0
        await asyncio.sleep(2)  # Simulate processing
        job.progress = 100.0
    
    async def _refresh_compliance_data(self, job: RefreshJob):
        """Refresh compliance data for an MSME"""
        # Implementation would call compliance service
        job.progress = 50.0
        await asyncio.sleep(1)  # Simulate processing
        job.progress = 100.0
    
    async def _schedule_automatic_refreshes(self):
        """Schedule automatic refreshes based on intervals"""
        while True:
            try:
                # Check all MSMEs for refresh needs
                msmes = await self._get_all_msmes()
                
                for msme_id in msmes:
                    for source, interval in self.refresh_intervals.items():
                        if await self._needs_refresh(msme_id, source, interval):
                            await self.schedule_refresh(msme_id, [source], RefreshPriority.LOW)
                
                # Wait 1 hour before checking again
                await asyncio.sleep(3600)
                
            except Exception as e:
                logger.error(f"Error in automatic refresh scheduler: {str(e)}")
                await asyncio.sleep(600)  # Wait 10 minutes on error
    
    async def _needs_refresh(self, msme_id: str, source: DataSource, interval: timedelta) -> bool:
        """Check if data source needs refresh"""
        try:
            # Get last refresh time
            docs = self.db.collection('refresh_jobs').where('msme_id', '==', msme_id).where('data_source', '==', source.value).where('status', '==', RefreshStatus.COMPLETED.value).order_by('completed_at', direction='DESCENDING').limit(1).stream()
            
            for doc in docs:
                data = doc.to_dict()
                last_refresh = data['completed_at']
                return datetime.utcnow() > last_refresh + interval
            
            # No previous refresh found
            return True
            
        except Exception as e:
            logger.error(f"Error checking refresh need: {str(e)}")
            return False
    
    async def _get_all_msmes(self) -> List[str]:
        """Get all MSME IDs"""
        try:
            # This would get all MSMEs from the database
            # For now, return mock data
            return ["MSME_001", "MSME_002", "MSME_003"]
        except Exception as e:
            logger.error(f"Error getting all MSMEs: {str(e)}")
            return []
    
    async def _store_refresh_job(self, job: RefreshJob):
        """Store refresh job in database"""
        try:
            doc_ref = self.db.collection('refresh_jobs').document(job.job_id)
            doc_ref.set({
                "job_id": job.job_id,
                "msme_id": job.msme_id,
                "data_source": job.data_source.value,
                "priority": job.priority.value,
                "status": job.status.value,
                "created_at": job.created_at,
                "started_at": job.started_at,
                "completed_at": job.completed_at,
                "error_message": job.error_message,
                "retry_count": job.retry_count,
                "progress": job.progress,
                "metadata": job.metadata
            })
        except Exception as e:
            logger.error(f"Error storing refresh job: {str(e)}")
